<!DOCTYPE html>
<html>
<head>
    <title>Test Budget API</title>
</head>
<body>
    <h1>Test Budget API</h1>
    <button onclick="testCreateBudget()">Test Create Budget</button>
    <button onclick="testGetBudgets()">Test Get Budgets</button>
    <div id="output"></div>
    
    <script>
        async function testCreateBudget() {
            const output = document.getElementById('output');
            output.innerHTML = '<p>Testing budget creation...</p>';
            
            const testBudget = {
                name: 'Test Budget',
                description: 'Test budget for API validation',
                startDate: '2024-12-01',
                endDate: '2024-12-31',
                totalIncome: 5000,
                strategy: 'test',
                budgetCategories: [
                    {
                        categoryId: 'test_cat',
                        categoryName: 'Test Category',
                        amount: 2500,
                        percentage: 50
                    }
                ]
            };
            
            try {
                console.log('Sending test budget:', testBudget);
                
                const response = await fetch('/api/budgets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testBudget)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    output.innerHTML = '<h3>✅ Success!</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                } else {
                    output.innerHTML = '<h3>❌ Error!</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                }
                
            } catch (error) {
                output.innerHTML = '<h3>❌ Network Error!</h3><p>' + error.message + '</p>';
            }
        }
        
        async function testGetBudgets() {
            const output = document.getElementById('output');
            output.innerHTML = '<p>Getting budgets...</p>';
            
            try {
                const response = await fetch('/api/budgets');
                const result = await response.json();
                
                if (response.ok) {
                    output.innerHTML = '<h3>✅ Budgets Retrieved!</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                } else {
                    output.innerHTML = '<h3>❌ Error!</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                }
                
            } catch (error) {
                output.innerHTML = '<h3>❌ Network Error!</h3><p>' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
