# Budget Creation Module - Enhanced Income Management Requirements

## Overview
This document outlines the comprehensive requirements for the Budget Creation Module with enhanced multiple income stream management capabilities.

## Table of Contents
1. [Enhanced Income Management Requirements](#enhanced-income-management-requirements)
2. [User Interface Requirements](#user-interface-requirements)
3. [Data Requirements](#data-requirements)
4. [Functional Requirements](#functional-requirements)
5. [User Workflow](#user-workflow)
6. [API Requirements](#api-requirements)
7. [Acceptance Criteria](#acceptance-criteria)
8. [Success Metrics](#success-metrics)

---

## Enhanced Income Management Requirements

### **BR-003: Enhanced Income and Total Budget Setup**

#### **BR-003.1: Multiple Income Streams Configuration**

##### **Income Stream Creation**
- **Stream Name**: User-defined text input (50-character limit)
  - Examples: "Primary Salary", "Freelance Work", "Rental Income", "Investment Dividends"
- **Stream Amount**: Decimal input with currency formatting
- **Frequency Selection**: Toggle between "Monthly" and "Annually"
- **Auto-Calculation**: System converts between monthly/annual based on selection
- **Stream Status**: Active/Inactive toggle for seasonal or temporary income
- **Stream Category**: Optional dropdown (Employment, Business, Investment, Other)

##### **Income Stream Management**
- **Add Stream**: "+" button to add additional income streams
- **Remove Stream**: Delete button with confirmation dialog
- **Reorder Streams**: Drag-and-drop reordering for priority display
- **Edit Stream**: Inline editing of name, amount, and frequency
- **Duplicate Stream**: Copy existing stream for similar income sources

##### **Income Validation and Calculations**
- **Minimum Streams**: At least 1 income stream required
- **Maximum Streams**: Up to 10 income streams allowed
- **Total Income Calculation**: Auto-sum all active streams
- **Frequency Conversion**: 
  - Annual to Monthly: Amount ÷ 12
  - Monthly to Annual: Amount × 12
- **Real-time Updates**: Total budget updates as income streams change

---

## User Interface Requirements

### **UI-001.1: Updated Step 1 - Budget Basics with Multiple Income Streams**

```
┌─────────────────────────────────────────────────┐
│ Create Your Budget                              │
├─────────────────────────────────────────────────┤
│ Budget Period                                   │
│ Start Date: [MM/DD/YYYY] 📅                    │
│ End Date:   [MM/DD/YYYY] 📅                    │
│ Duration: 12 months                             │
│                                                 │
│ Income Streams                                  │
│ ┌─────────────────────────────────────────────┐ │
│ │ 💼 Primary Salary                           │ │
│ │ Amount: $4,200  ○ Monthly ○ Annually       │ │
│ │ Status: ✅ Active                           │ │
│ │                                    [Edit][×]│ │
│ └─────────────────────────────────────────────┘ │
│                                                 │
│ ┌─────────────────────────────────────────────┐ │
│ │ 💻 Freelance Work                           │ │
│ │ Amount: $12,000  ○ Monthly ● Annually      │ │
│ │ Status: ✅ Active                           │ │
│ │                                    [Edit][×]│ │
│ └─────────────────────────────────────────────┘ │
│                                                 │
│ ┌─────────────────────────────────────────────┐ │
│ │ 🏠 Rental Income                            │ │
│ │ Amount: $800  ● Monthly ○ Annually         │ │
│ │ Status: ✅ Active                           │ │
│ │                                    [Edit][×]│ │
│ └─────────────────────────────────────────────┘ │
│                                                 │
│ [+ Add Income Stream]                           │
│                                                 │
│ ┌─────────────────────────────────────────────┐ │
│ │ Total Annual Income:  $62,400               │ │
│ │ Total Monthly Income: $5,200                │ │
│ └─────────────────────────────────────────────┘ │
│                                                 │
│ [Continue] [Save Draft]                         │
└─────────────────────────────────────────────────┘
```

### **UI-001.2: Add/Edit Income Stream Modal**

```
┌─────────────────────────────────────┐
│ Add Income Stream                   │
├─────────────────────────────────────┤
│ Stream Name *                       │
│ [Freelance Web Development    ]     │
│                                     │
│ Amount *                            │
│ $[15,000.00]                       │
│                                     │
│ Frequency *                         │
│ ○ Monthly   ● Annually             │
│                                     │
│ Category (Optional)                 │
│ [Business Income        ▼]         │
│                                     │
│ Status                              │
│ ✅ Active  ☐ Seasonal/Inactive     │
│                                     │
│ Notes (Optional)                    │
│ [Contract work, varies by quarter] │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ Preview:                        │ │
│ │ Monthly: $1,250.00             │ │
│ │ Annual:  $15,000.00            │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [Cancel] [Save Stream]              │
└─────────────────────────────────────┘
```

### **UI-001.3: Income Stream Categories**

Predefined categories for better organization:
- **Employment**: Primary salary, part-time job, overtime
- **Business**: Freelance, consulting, business profits
- **Investment**: Dividends, capital gains, rental income
- **Government**: Social security, unemployment, disability
- **Other**: Gifts, alimony, miscellaneous

---

## Data Requirements

### **DB-001.1: Income Stream Schema**

```typescript
model Budget {
  id           String   @id @default(cuid())
  name         String
  userId       String
  startDate    DateTime
  endDate      DateTime
  totalAnnualAmount  Decimal  // Calculated from all income streams
  totalMonthlyAmount Decimal  // Calculated from all income streams
  templateType String
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  // Relationships
  user         User     @relation(fields: [userId], references: [id])
  incomeStreams IncomeStream[]
  sections     BudgetSection[]
  
  @@map("budgets")
}

model IncomeStream {
  id          String   @id @default(cuid())
  budgetId    String
  name        String   // "Primary Salary", "Freelance Work"
  amount      Decimal  // Stored amount (as entered by user)
  frequency   String   // "monthly" or "annually"
  monthlyAmount Decimal // Always calculated for consistency
  annualAmount  Decimal // Always calculated for consistency
  category    String?  // "employment", "business", "investment", "government", "other"
  isActive    Boolean  @default(true)
  notes       String?
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  budget      Budget   @relation(fields: [budgetId], references: [id], onDelete: Cascade)
  
  @@map("income_streams")
}
```

---

## Functional Requirements

### **BR-003.2: Income Stream Business Logic**

#### **Calculation Rules**
```typescript
// Auto-calculation based on frequency
if (frequency === 'monthly') {
  monthlyAmount = amount
  annualAmount = amount * 12
} else if (frequency === 'annually') {
  annualAmount = amount
  monthlyAmount = amount / 12
}

// Total budget calculation
totalMonthlyIncome = sum(activeIncomeStreams.monthlyAmount)
totalAnnualIncome = sum(activeIncomeStreams.annualAmount)
```

#### **Validation Rules**
- **Required Fields**: Stream name and amount are mandatory
- **Amount Validation**: Must be positive number, max 2 decimal places
- **Name Validation**: Must be unique within the budget
- **Active Stream Requirement**: At least one stream must be active
- **Reasonable Limits**: Individual stream max $1,000,000 annually

#### **Default Stream Behavior**
- **First Stream**: Auto-named "Primary Income" if user doesn't specify
- **Default Frequency**: Monthly (most common for salary earners)
- **Default Category**: Employment
- **Default Status**: Active

### **BR-003.3: Income Stream Templates**

#### **Quick Setup Templates**
- **Single Salary**: One primary employment income
- **Salary + Side Hustle**: Primary job + freelance/part-time
- **Multiple Jobs**: 2-3 employment streams
- **Investor**: Multiple investment income streams
- **Retiree**: Pension, social security, investment income
- **Small Business Owner**: Business income + other sources

#### **Template Application**
- User selects template during budget creation
- System creates appropriate income streams with placeholder names
- User customizes amounts and names
- Maintains template structure while allowing full customization

---

## User Workflow

### **WF-001: Income Stream Management Workflow**

#### **Step 1A: Income Setup (Enhanced)**
1. **Initial Setup**:
   - User enters budget period
   - System prompts for income streams
   - Option to use template or start from scratch

2. **Stream Creation**:
   - Click "Add Income Stream"
   - Fill stream details in modal
   - Preview monthly/annual calculations
   - Save and see updated totals

3. **Stream Management**:
   - Edit existing streams inline or in modal
   - Toggle active/inactive status for seasonal income
   - Reorder streams by importance
   - Delete unused streams

4. **Validation & Continuation**:
   - Ensure at least one active stream
   - Verify total income is reasonable
   - Proceed to template selection

#### **Step 1B: Template Selection (Updated)**
Template selection now considers total income for better recommendations:
- Show percentage allocations based on total income
- Adjust recommendations for high/low income levels
- Consider income stability (single vs multiple streams)

---

## API Requirements

### **API-001: Income Stream Endpoints**

```typescript
// Create income stream
POST /api/budgets/{budgetId}/income-streams
{
  "name": "Freelance Work",
  "amount": 15000,
  "frequency": "annually",
  "category": "business",
  "isActive": true,
  "notes": "Varies by quarter"
}

// Update income stream
PUT /api/budgets/{budgetId}/income-streams/{streamId}
{
  "name": "Updated Freelance Work",
  "amount": 18000,
  "frequency": "annually"
}

// Get all income streams for budget
GET /api/budgets/{budgetId}/income-streams
Response: {
  "streams": [...],
  "totals": {
    "monthlyTotal": 5200,
    "annualTotal": 62400,
    "activeStreams": 3
  }
}

// Reorder income streams
PUT /api/budgets/{budgetId}/income-streams/reorder
{
  "streamIds": ["id1", "id2", "id3"]
}

// Toggle stream status
PATCH /api/budgets/{budgetId}/income-streams/{streamId}/toggle
{
  "isActive": false
}
```

---

## Acceptance Criteria

### **AC-001.1: Income Stream Management**
- ✅ User can add multiple income streams with unique names
- ✅ User can set each stream as monthly or annually
- ✅ System auto-calculates and displays both monthly and annual totals
- ✅ User can edit, reorder, and delete income streams
- ✅ User can toggle streams active/inactive
- ✅ System validates at least one active stream exists
- ✅ Total budget calculations update in real-time

### **AC-001.2: Income Stream Data Integrity**
- ✅ Monthly/annual conversions are mathematically accurate
- ✅ Inactive streams don't contribute to total income
- ✅ Stream names must be unique within a budget
- ✅ Deleted streams don't affect historical data integrity
- ✅ Budget totals recalculate when streams change

### **AC-001.3: User Experience**
- ✅ Income stream interface is intuitive and responsive
- ✅ Modal forms provide clear validation feedback
- ✅ Frequency toggle shows immediate amount conversion
- ✅ Total income display is prominently visible
- ✅ Bulk operations available for multiple streams

---

## Success Metrics

### **Quantitative Metrics**
- **Multi-Stream Adoption**: >40% of users create multiple income streams
- **Stream Management**: <5% of users delete streams after creation
- **Calculation Accuracy**: 100% accuracy in monthly/annual conversions
- **Template Usage**: >30% use income stream templates

### **Qualitative Metrics**
- **User Satisfaction**: >4.5/5 for income setup experience
- **Feature Usefulness**: >80% find multiple streams helpful
- **Support Reduction**: <1% of income-related support tickets

---

## Implementation Priority

### **Phase 1: Core Income Stream Management**
1. Basic income stream CRUD operations
2. Monthly/annual frequency conversion
3. Real-time total calculations
4. Basic validation rules

### **Phase 2: Enhanced Features**
1. Income stream templates
2. Drag-and-drop reordering
3. Advanced validation and error handling
4. Bulk operations

### **Phase 3: Advanced Functionality**
1. Income forecasting and projections
2. Historical income tracking
3. Integration with external financial services
4. Advanced reporting and analytics

---

## Technical Considerations

### **Performance Requirements**
- Income calculations must complete within 100ms
- Real-time updates should not cause UI lag
- Support for up to 10 income streams per budget
- Efficient database queries for income aggregation

### **Security Requirements**
- Income data must be encrypted at rest
- API endpoints require proper authentication
- Input validation to prevent injection attacks
- Audit logging for income stream modifications

### **Accessibility Requirements**
- Screen reader compatible form inputs
- Keyboard navigation for all income stream operations
- High contrast mode support
- Mobile-responsive design for all screen sizes

This enhanced income management system provides comprehensive support for modern users who often have multiple income sources, while maintaining simplicity for those with single income streams.
