<!DOCTYPE html>
<html>
<head>
    <title>Fix Budget Data</title>
</head>
<body>
    <h1>Fix Budget Data</h1>
    <button onclick="fixBudgetData()">Fix Budget Data</button>
    <button onclick="showBudgetData()">Show Budget Data</button>
    <div id="output"></div>
    
    <script>
        function showBudgetData() {
            const budgets = localStorage.getItem('budgets');
            const output = document.getElementById('output');
            
            if (budgets) {
                const budgetList = JSON.parse(budgets);
                output.innerHTML = '<h2>Found ' + budgetList.length + ' budgets:</h2>';
                
                budgetList.forEach((budget, index) => {
                    output.innerHTML += '<h3>Budget ' + (index + 1) + ': ' + budget.name + '</h3>';
                    output.innerHTML += '<p><strong>ID:</strong> ' + budget.id + '</p>';
                    output.innerHTML += '<p><strong>Strategy:</strong> ' + budget.strategy + '</p>';
                    output.innerHTML += '<p><strong>Categories Count:</strong> ' + (budget.budgetCategories ? budget.budgetCategories.length : 0) + '</p>';
                    output.innerHTML += '<pre>' + JSON.stringify(budget.budgetCategories, null, 2) + '</pre>';
                    output.innerHTML += '<hr>';
                });
            } else {
                output.innerHTML = '<p>No budgets found in localStorage</p>';
            }
        }
        
        function fixBudgetData() {
            const budgets = localStorage.getItem('budgets');
            
            if (budgets) {
                const budgetList = JSON.parse(budgets);
                
                budgetList.forEach(budget => {
                    // Fix budget with ID budget_1749901973532_bs9buwtqi if it has no categories
                    if (budget.id === 'budget_1749901973532_bs9buwtqi' && (!budget.budgetCategories || budget.budgetCategories.length === 0)) {
                        console.log('Fixing budget:', budget.id);
                        
                        // Add sample categories for this budget
                        budget.budgetCategories = [
                            {
                                id: 'category_needs',
                                categoryId: 'needs',
                                categoryName: 'Essential Expenses',
                                amount: 2500,
                                percentage: 50,
                                spent: 0,
                                remaining: 2500,
                                color: 'blue',
                                description: 'Must-have expenses you cannot avoid'
                            },
                            {
                                id: 'category_wants',
                                categoryId: 'wants',
                                categoryName: 'Lifestyle & Fun',
                                amount: 1500,
                                percentage: 30,
                                spent: 0,
                                remaining: 1500,
                                color: 'orange',
                                description: 'Things you enjoy but could live without'
                            },
                            {
                                id: 'category_savings',
                                categoryId: 'savings',
                                categoryName: 'Future & Goals',
                                amount: 1000,
                                percentage: 20,
                                spent: 0,
                                remaining: 1000,
                                color: 'green',
                                description: 'Building your financial future'
                            }
                        ];
                        
                        // Mark as custom budget
                        budget.strategy = 'custom';
                        budget.selectedTemplate = {
                            id: 'custom',
                            name: 'Custom Strategy',
                            isCustom: true,
                            customCategories: [
                                {
                                    id: 'needs',
                                    name: 'Essential Expenses',
                                    percentage: 50,
                                    color: 'blue',
                                    description: 'Must-have expenses you cannot avoid'
                                },
                                {
                                    id: 'wants',
                                    name: 'Lifestyle & Fun',
                                    percentage: 30,
                                    color: 'orange',
                                    description: 'Things you enjoy but could live without'
                                },
                                {
                                    id: 'savings',
                                    name: 'Future & Goals',
                                    percentage: 20,
                                    color: 'green',
                                    description: 'Building your financial future'
                                }
                            ]
                        };
                    }
                });
                
                localStorage.setItem('budgets', JSON.stringify(budgetList));
                alert('Budget data fixed! Refresh the budget view page.');
            }
        }
        
        // Show data on load
        showBudgetData();
    </script>
</body>
</html>
