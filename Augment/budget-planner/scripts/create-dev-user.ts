import { prisma } from '../src/lib/prisma'

async function createDevUser() {
  console.log('🔄 Creating development user...')
  
  try {
    // Check if dev user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingUser) {
      console.log('✅ Development user already exists:', existingUser.id)
      return existingUser
    }

    // Create development user
    const devUser = await prisma.user.create({
      data: {
        id: 'dev_user_123',
        name: 'Development User',
        email: '<EMAIL>',
        password: 'dev_password_hash' // In real app, this would be hashed
      }
    })

    console.log('✅ Development user created successfully!')
    console.log('📧 Email:', devUser.email)
    console.log('🆔 ID:', devUser.id)
    
    return devUser
    
  } catch (error) {
    console.error('❌ Error creating development user:', error)
    throw error
  }
}

async function main() {
  try {
    await createDevUser()
    console.log('🎉 Development user setup completed!')
  } catch (error) {
    console.error('❌ Development user setup failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((error) => {
    console.error('❌ Script failed:', error)
    process.exit(1)
  })
