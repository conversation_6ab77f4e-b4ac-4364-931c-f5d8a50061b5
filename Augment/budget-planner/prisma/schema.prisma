generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String        @id @default(cuid())
  name          String?
  email         String        @unique
  password      String
  image         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  budgets       Budget[]
  transactions  Transaction[]
  notifications Notification[]
}

model Category {
  id               String           @id @default(cuid())
  name             String
  description      String?
  parentId         String?
  parent           Category?        @relation("CategoryToSubcategory", fields: [parentId], references: [id])
  subcategories    Category[]       @relation("CategoryToSubcategory")
  budgetCategories BudgetCategory[]
  transactions     Transaction[]
}

model Budget {
  id               String           @id @default(cuid())
  name             String
  description      String?
  startDate        DateTime
  endDate          DateTime
  userId           String
  user             User             @relation(fields: [userId], references: [id])
  budgetCategories BudgetCategory[]
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
}

model BudgetCategory {
  id         String   @id @default(cuid())
  budgetId   String
  categoryId String
  amount     Float
  budget     Budget   @relation(fields: [budgetId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id])
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([budgetId, categoryId])
}

model Transaction {
  id          String   @id @default(cuid())
  amount      Float
  date        DateTime
  description String?
  userId      String
  categoryId  String
  user        User     @relation(fields: [userId], references: [id])
  category    Category @relation(fields: [categoryId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  message   String
  read      Boolean  @default(false)
  type      String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
