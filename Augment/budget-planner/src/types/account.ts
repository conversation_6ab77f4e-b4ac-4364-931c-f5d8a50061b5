// 🏦 Multi-Account Financial Management Types

export type AccountType = 
  | 'checking' 
  | 'savings' 
  | 'credit_card' 
  | 'cash' 
  | 'investment' 
  | 'digital_wallet'
  | 'loan'
  | 'mortgage'

export type AccountProvider = 
  | 'chase' 
  | 'bank_of_america' 
  | 'wells_fargo' 
  | 'citi' 
  | 'capital_one'
  | 'american_express'
  | 'discover'
  | 'visa'
  | 'mastercard'
  | 'paypal'
  | 'venmo'
  | 'cash_app'
  | 'zelle'
  | 'other'

export interface FinancialAccount {
  id: string
  userId: string
  name: string // "Chase Freedom", "Wells Fargo Checking", "Cash Wallet"
  type: AccountType
  provider: AccountProvider
  accountNumber?: string // Last 4 digits for display
  routingNumber?: string
  balance?: number
  availableBalance?: number
  creditLimit?: number // For credit cards
  interestRate?: number
  isActive: boolean
  isPrimary: boolean // Primary account for each type
  color: string // For UI visualization
  icon: string // Icon identifier
  currency: string
  description?: string
  lastSyncDate?: Date
  createdAt: Date
  updatedAt: Date
}

export interface AccountBalance {
  accountId: string
  balance: number
  availableBalance?: number
  asOfDate: Date
  pending?: number
  overdraftLimit?: number
}

// 🎨 Account Display Configuration
export const ACCOUNT_TYPE_CONFIG = {
  checking: {
    label: 'Checking Account',
    icon: 'building-2',
    color: '#3B82F6',
    description: 'Primary spending account'
  },
  savings: {
    label: 'Savings Account',
    icon: 'piggy-bank',
    color: '#10B981',
    description: 'Savings and emergency funds'
  },
  credit_card: {
    label: 'Credit Card',
    icon: 'credit-card',
    color: '#EF4444',
    description: 'Credit card purchases'
  },
  cash: {
    label: 'Cash',
    icon: 'banknote',
    color: '#F59E0B',
    description: 'Cash transactions'
  },
  investment: {
    label: 'Investment Account',
    icon: 'trending-up',
    color: '#8B5CF6',
    description: 'Investment and brokerage accounts'
  },
  digital_wallet: {
    label: 'Digital Wallet',
    icon: 'smartphone',
    color: '#06B6D4',
    description: 'PayPal, Venmo, Cash App, etc.'
  },
  loan: {
    label: 'Loan Account',
    icon: 'file-text',
    color: '#F97316',
    description: 'Personal loans and lines of credit'
  },
  mortgage: {
    label: 'Mortgage',
    icon: 'home',
    color: '#84CC16',
    description: 'Home mortgage account'
  }
}

export const ACCOUNT_PROVIDER_CONFIG = {
  chase: {
    label: 'Chase Bank',
    icon: 'chase-logo',
    color: '#0066B2',
    csvFormat: 'chase'
  },
  bank_of_america: {
    label: 'Bank of America',
    icon: 'boa-logo',
    color: '#E31837',
    csvFormat: 'bank_of_america'
  },
  wells_fargo: {
    label: 'Wells Fargo',
    icon: 'wells-fargo-logo',
    color: '#D71921',
    csvFormat: 'wells_fargo'
  },
  citi: {
    label: 'Citibank',
    icon: 'citi-logo',
    color: '#056DAE',
    csvFormat: 'generic'
  },
  capital_one: {
    label: 'Capital One',
    icon: 'capital-one-logo',
    color: '#004879',
    csvFormat: 'generic'
  },
  american_express: {
    label: 'American Express',
    icon: 'amex-logo',
    color: '#006FCF',
    csvFormat: 'generic'
  },
  discover: {
    label: 'Discover',
    icon: 'discover-logo',
    color: '#FF6000',
    csvFormat: 'generic'
  },
  visa: {
    label: 'Visa',
    icon: 'visa-logo',
    color: '#1A1F71',
    csvFormat: 'generic'
  },
  mastercard: {
    label: 'Mastercard',
    icon: 'mastercard-logo',
    color: '#EB001B',
    csvFormat: 'generic'
  },
  paypal: {
    label: 'PayPal',
    icon: 'paypal-logo',
    color: '#0070BA',
    csvFormat: 'generic'
  },
  venmo: {
    label: 'Venmo',
    icon: 'venmo-logo',
    color: '#3D95CE',
    csvFormat: 'generic'
  },
  cash_app: {
    label: 'Cash App',
    icon: 'cash-app-logo',
    color: '#00D632',
    csvFormat: 'generic'
  },
  zelle: {
    label: 'Zelle',
    icon: 'zelle-logo',
    color: '#6C1D9A',
    csvFormat: 'generic'
  },
  other: {
    label: 'Other',
    icon: 'building',
    color: '#6B7280',
    csvFormat: 'generic'
  }
}

// 🔄 Account Transaction Integration
export interface EnhancedTransaction {
  id: string
  accountId: string // Which account this transaction belongs to
  account?: FinancialAccount // Populated account details
  date: Date
  description: string
  amount: number
  type: 'expense' | 'income' | 'transfer'
  category: {
    id: string
    name: string
    type: 'NEED' | 'WANT' | 'SMILE'
    icon: string
    color: string
  }
  subcategory?: string
  confidence: number
  isRecurring?: boolean
  merchantName?: string
  originalDescription: string
  userId: string
  budgetId?: string
  tags?: string[]
  notes?: string
  receiptUrl?: string
  isReviewed: boolean
  
  // Transfer-specific fields
  transferToAccountId?: string // For account-to-account transfers
  transferFromAccountId?: string
  
  // Account-specific fields
  runningBalance?: number // Account balance after this transaction
  checkNumber?: string // For check transactions
  referenceNumber?: string
  
  createdAt: Date
  updatedAt: Date
}

// 📊 Account Summary Statistics
export interface AccountSummary {
  accountId: string
  account: FinancialAccount
  currentBalance: number
  monthlyIncome: number
  monthlyExpenses: number
  transactionCount: number
  lastTransactionDate?: Date
  trends: {
    balanceChange: number
    balanceChangePercent: number
    expenseChange: number
    expenseChangePercent: number
  }
}

// 🔄 Account Transfer
export interface AccountTransfer {
  id: string
  fromAccountId: string
  toAccountId: string
  amount: number
  description: string
  date: Date
  fee?: number
  status: 'pending' | 'completed' | 'failed'
  referenceNumber?: string
  userId: string
  createdAt: Date
}

// 📱 Account Management Actions
export type AccountAction = 
  | 'add_transaction'
  | 'import_csv'
  | 'transfer_funds'
  | 'update_balance'
  | 'sync_account'
  | 'view_statements'
  | 'export_data'

export interface AccountActionConfig {
  action: AccountAction
  label: string
  icon: string
  description: string
  availableFor: AccountType[]
  requiresConnection?: boolean
}

export const ACCOUNT_ACTIONS: AccountActionConfig[] = [
  {
    action: 'add_transaction',
    label: 'Add Transaction',
    icon: 'plus',
    description: 'Manually add a transaction',
    availableFor: ['checking', 'savings', 'credit_card', 'cash', 'digital_wallet']
  },
  {
    action: 'import_csv',
    label: 'Import CSV',
    icon: 'upload',
    description: 'Import transactions from bank CSV',
    availableFor: ['checking', 'savings', 'credit_card']
  },
  {
    action: 'transfer_funds',
    label: 'Transfer Funds',
    icon: 'arrow-right-left',
    description: 'Transfer money between accounts',
    availableFor: ['checking', 'savings']
  },
  {
    action: 'update_balance',
    label: 'Update Balance',
    icon: 'refresh-cw',
    description: 'Manually update account balance',
    availableFor: ['checking', 'savings', 'cash', 'digital_wallet']
  },
  {
    action: 'sync_account',
    label: 'Sync Account',
    icon: 'refresh-cw',
    description: 'Sync with bank (requires connection)',
    availableFor: ['checking', 'savings', 'credit_card'],
    requiresConnection: true
  },
  {
    action: 'view_statements',
    label: 'View Statements',
    icon: 'file-text',
    description: 'View account statements',
    availableFor: ['checking', 'savings', 'credit_card']
  },
  {
    action: 'export_data',
    label: 'Export Data',
    icon: 'download',
    description: 'Export transaction data',
    availableFor: ['checking', 'savings', 'credit_card', 'cash', 'digital_wallet']
  }
]
