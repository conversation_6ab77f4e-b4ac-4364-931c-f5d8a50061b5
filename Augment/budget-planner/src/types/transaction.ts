// 🏦 Bank Transaction Import & Categorization Types

export interface RawTransaction {
  id?: string
  date: string
  description: string
  amount: number
  type?: 'debit' | 'credit' | 'expense' | 'income'
  balance?: number
  reference?: string
  category?: string
}

export interface ProcessedTransaction {
  id: string
  accountId: string // Which account this transaction belongs to
  date: Date
  description: string
  amount: number
  type: 'expense' | 'income' | 'transfer'
  category: ExpenseCategory
  subcategory?: string
  confidence: number // AI categorization confidence (0-1)
  isRecurring?: boolean
  merchantName?: string
  originalDescription: string
  userId: string
  budgetId?: string
  tags?: string[]
  notes?: string
  receiptUrl?: string
  isReviewed: boolean

  // Transfer-specific fields
  transferToAccountId?: string
  transferFromAccountId?: string

  // Account-specific fields
  runningBalance?: number
  checkNumber?: string
  referenceNumber?: string

  createdAt: Date
  updatedAt: Date
}

export interface ExpenseCategory {
  id: string
  name: string
  type: 'NEED' | 'WANT' | 'SMILE'
  icon: string
  color: string
  keywords: string[]
  subcategories: ExpenseSubcategory[]
}

export interface ExpenseSubcategory {
  id: string
  name: string
  keywords: string[]
  parentCategoryId: string
}

// 🏦 Supported Bank CSV Formats
export interface BankFormat {
  id: string
  name: string
  columns: {
    date: string[]
    description: string[]
    amount: string[]
    type?: string[]
    balance?: string[]
    reference?: string[]
  }
  dateFormat: string
  amountFormat: 'positive_negative' | 'separate_columns' | 'debit_credit'
  skipRows: number
  encoding: string
}

export const SUPPORTED_BANK_FORMATS: BankFormat[] = [
  {
    id: 'chase',
    name: 'Chase Bank',
    columns: {
      date: ['Transaction Date', 'Date'],
      description: ['Description'],
      amount: ['Amount'],
      type: ['Type'],
      balance: ['Balance']
    },
    dateFormat: 'MM/DD/YYYY',
    amountFormat: 'positive_negative',
    skipRows: 1,
    encoding: 'utf-8'
  },
  {
    id: 'bank_of_america',
    name: 'Bank of America',
    columns: {
      date: ['Date'],
      description: ['Description'],
      amount: ['Amount'],
      balance: ['Running Bal.']
    },
    dateFormat: 'MM/DD/YYYY',
    amountFormat: 'positive_negative',
    skipRows: 1,
    encoding: 'utf-8'
  },
  {
    id: 'wells_fargo',
    name: 'Wells Fargo',
    columns: {
      date: ['Date'],
      description: ['Description'],
      amount: ['Amount']
    },
    dateFormat: 'MM/DD/YYYY',
    amountFormat: 'positive_negative',
    skipRows: 1,
    encoding: 'utf-8'
  },
  {
    id: 'generic',
    name: 'Generic CSV',
    columns: {
      date: ['Date', 'Transaction Date', 'date'],
      description: ['Description', 'Memo', 'description'],
      amount: ['Amount', 'amount'],
      type: ['Type', 'Transaction Type', 'type']
    },
    dateFormat: 'auto-detect',
    amountFormat: 'positive_negative',
    skipRows: 0,
    encoding: 'utf-8'
  }
]

// 🤖 Smart Categorization Rules
export interface CategorizationRule {
  id: string
  category: string
  subcategory: string
  keywords: string[]
  patterns: RegExp[]
  confidence: number
  priority: number
}

export const DEFAULT_CATEGORIZATION_RULES: CategorizationRule[] = [
  // NEED Categories
  {
    id: 'housing_rent',
    category: 'Housing',
    subcategory: 'Rent/Mortgage',
    keywords: ['rent', 'mortgage', 'property management', 'landlord'],
    patterns: [/rent/i, /mortgage/i, /property/i],
    confidence: 0.9,
    priority: 1
  },
  {
    id: 'utilities',
    category: 'Utilities',
    subcategory: 'Electricity & Gas',
    keywords: ['electric', 'gas', 'utility', 'power', 'energy'],
    patterns: [/electric/i, /gas/i, /utility/i, /power/i],
    confidence: 0.85,
    priority: 1
  },
  {
    id: 'groceries',
    category: 'Food & Dining',
    subcategory: 'Groceries',
    keywords: ['grocery', 'supermarket', 'walmart', 'target', 'costco', 'safeway'],
    patterns: [/grocery/i, /supermarket/i, /walmart/i, /target/i],
    confidence: 0.8,
    priority: 1
  },
  
  // WANT Categories
  {
    id: 'dining_out',
    category: 'Food & Dining',
    subcategory: 'Restaurants',
    keywords: ['restaurant', 'cafe', 'starbucks', 'mcdonalds', 'pizza'],
    patterns: [/restaurant/i, /cafe/i, /starbucks/i, /pizza/i],
    confidence: 0.75,
    priority: 2
  },
  {
    id: 'entertainment',
    category: 'Entertainment',
    subcategory: 'Movies & Shows',
    keywords: ['netflix', 'spotify', 'movie', 'theater', 'cinema'],
    patterns: [/netflix/i, /spotify/i, /movie/i, /theater/i],
    confidence: 0.8,
    priority: 2
  },
  {
    id: 'shopping',
    category: 'Shopping',
    subcategory: 'Retail',
    keywords: ['amazon', 'ebay', 'shopping', 'store', 'mall'],
    patterns: [/amazon/i, /ebay/i, /shopping/i, /store/i],
    confidence: 0.7,
    priority: 2
  },
  
  // SMILE Categories
  {
    id: 'savings',
    category: 'Savings',
    subcategory: 'Emergency Fund',
    keywords: ['transfer to savings', 'savings account', 'emergency fund'],
    patterns: [/savings/i, /emergency/i, /transfer.*savings/i],
    confidence: 0.9,
    priority: 1
  }
]

// 📊 Import Statistics
export interface ImportStats {
  totalTransactions: number
  successfulImports: number
  duplicates: number
  errors: number
  categorized: number
  uncategorized: number
  dateRange: {
    start: Date
    end: Date
  }
  categories: {
    [category: string]: {
      count: number
      amount: number
    }
  }
}

// 🔄 Import Status
export type ImportStatus = 'pending' | 'processing' | 'completed' | 'error'

export interface ImportJob {
  id: string
  userId: string
  fileName: string
  fileSize: number
  status: ImportStatus
  progress: number
  stats?: ImportStats
  errors?: string[]
  createdAt: Date
  completedAt?: Date
}
