'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AppLayout } from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import * as Icons from 'lucide-react'

interface IncomeSource {
  id: string
  name: string
  type: 'salary' | 'freelance' | 'business' | 'investment' | 'rental' | 'other'
  amount: number
  frequency: 'monthly' | 'annually' | 'weekly' | 'bi-weekly'
  monthlyAmount: number
  category: 'employment' | 'business' | 'passive' | 'government' | 'other'
  status: 'active' | 'inactive' | 'pending'
  lastReceived: string
  nextExpected: string
  reliability: number // 0-100%
  growth: number // percentage change
}

interface IncomeCategory {
  id: string
  name: string
  type: 'employment' | 'business' | 'passive' | 'government' | 'other'
  totalAmount: number
  sourceCount: number
  percentage: number
  color: string
  growth: number
}

// Mock data for professional design
const currentIncomeOverview = {
  totalMonthlyIncome: 6750.00,
  totalAnnualIncome: 81000.00,
  thisMonth: 6750.00,
  lastMonth: 6250.00,
  averageMonthly: 6500.00,
  activeSources: 4,
  growthRate: 8.0,
  reliability: 95,
  period: 'November 1 - November 30, 2024'
}

const incomeCategories: IncomeCategory[] = [
  {
    id: '1',
    name: 'Employment',
    type: 'employment',
    totalAmount: 5000.00,
    sourceCount: 1,
    percentage: 74,
    color: 'blue',
    growth: 5.2
  },
  {
    id: '2',
    name: 'Business',
    type: 'business',
    totalAmount: 1200.00,
    sourceCount: 2,
    percentage: 18,
    color: 'purple',
    growth: 15.8
  },
  {
    id: '3',
    name: 'Passive Income',
    type: 'passive',
    totalAmount: 550.00,
    sourceCount: 3,
    percentage: 8,
    color: 'green',
    growth: 12.3
  }
]

const mockIncomeSources: IncomeSource[] = [
  {
    id: '1',
    name: 'Primary Salary - Tech Corp',
    type: 'salary',
    amount: 60000,
    frequency: 'annually',
    monthlyAmount: 5000.00,
    category: 'employment',
    status: 'active',
    lastReceived: '2024-11-01',
    nextExpected: '2024-12-01',
    reliability: 100,
    growth: 5.2
  },
  {
    id: '2',
    name: 'Freelance Web Development',
    type: 'freelance',
    amount: 800,
    frequency: 'monthly',
    monthlyAmount: 800.00,
    category: 'business',
    status: 'active',
    lastReceived: '2024-11-15',
    nextExpected: '2024-12-15',
    reliability: 85,
    growth: 20.5
  },
  {
    id: '3',
    name: 'Consulting Services',
    type: 'business',
    amount: 400,
    frequency: 'monthly',
    monthlyAmount: 400.00,
    category: 'business',
    status: 'active',
    lastReceived: '2024-11-10',
    nextExpected: '2024-12-10',
    reliability: 75,
    growth: 10.2
  },
  {
    id: '4',
    name: 'Investment Dividends',
    type: 'investment',
    amount: 200,
    frequency: 'monthly',
    monthlyAmount: 200.00,
    category: 'passive',
    status: 'active',
    lastReceived: '2024-11-05',
    nextExpected: '2024-12-05',
    reliability: 90,
    growth: 8.7
  },
  {
    id: '5',
    name: 'Rental Property Income',
    type: 'rental',
    amount: 350,
    frequency: 'monthly',
    monthlyAmount: 350.00,
    category: 'passive',
    status: 'active',
    lastReceived: '2024-11-01',
    nextExpected: '2024-12-01',
    reliability: 95,
    growth: 15.0
  }
]

export default function IncomePage() {
  const router = useRouter()
  const [incomeSources, setIncomeSources] = useState<IncomeSource[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedSourceId, setSelectedSourceId] = useState<string | null>(null)

  // Mock income data - in production, this would come from API/database
  useEffect(() => {
    setTimeout(() => {
      setIncomeSources(mockIncomeSources)
      setLoading(false)
    }, 800)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-50 text-green-700 border-green-200'
      case 'inactive': return 'bg-gray-50 text-gray-700 border-gray-200'
      case 'pending': return 'bg-orange-50 text-orange-700 border-orange-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Icons.CheckCircle className="w-4 h-4" />
      case 'inactive': return <Icons.Pause className="w-4 h-4" />
      case 'pending': return <Icons.Clock className="w-4 h-4" />
      default: return <Icons.Circle className="w-4 h-4" />
    }
  }

  const getIncomeIcon = (type: string) => {
    switch (type) {
      case 'salary': return <Icons.Building2 className="w-6 h-6" />
      case 'freelance': return <Icons.Laptop className="w-6 h-6" />
      case 'business': return <Icons.Briefcase className="w-6 h-6" />
      case 'investment': return <Icons.TrendingUp className="w-6 h-6" />
      case 'rental': return <Icons.Home className="w-6 h-6" />
      default: return <Icons.DollarSign className="w-6 h-6" />
    }
  }

  const getReliabilityColor = (reliability: number) => {
    if (reliability >= 90) return 'text-green-600'
    if (reliability >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600'
    if (growth < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  if (loading) {
    return (
      <AppLayout>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-green-50/30">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-emerald-700 rounded-3xl flex items-center justify-center mx-auto animate-pulse">
              <Icons.TrendingUp className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-slate-900">Loading Income...</h2>
            <p className="text-slate-600">Preparing your income overview</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-green-50/30 -m-6">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(34,197,94,0.15)_1px,transparent_0)] bg-[length:24px_24px] opacity-30 pointer-events-none"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">

          {/* Header Section */}
          <div className="space-y-8 mb-12">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-slate-500">
              <span className="text-slate-900 font-semibold">Income Management</span>
            </nav>

            {/* Main Header */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-6 lg:space-y-0">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-emerald-700 rounded-3xl flex items-center justify-center shadow-xl shadow-green-600/25">
                    <Icons.TrendingUp className="w-8 h-8 text-white" strokeWidth={1.5} />
                  </div>
                  <div>
                    <h1 className="text-4xl lg:text-5xl font-bold text-gradient tracking-tight">
                      My Income
                    </h1>
                    <p className="text-lg text-slate-600 font-medium">
                      Track and manage all your income sources
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center space-x-3">
                <Button
                  onClick={() => setShowAddModal(true)}
                  className="flex items-center space-x-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Icons.Plus className="w-5 h-5" />
                  <span>Add Income Source</span>
                </Button>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Icons.Download className="w-4 h-4" />
                  <span>Export</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Income Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <Icons.DollarSign className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-900">
                      ${currentIncomeOverview.totalMonthlyIncome.toLocaleString()}
                    </div>
                    <div className="text-sm text-green-600 font-medium">Monthly Income</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <Icons.TrendingUp className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-900">
                      +{currentIncomeOverview.growthRate}%
                    </div>
                    <div className="text-sm text-green-600 font-medium">Growth Rate</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <Icons.Target className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-900">
                      {currentIncomeOverview.reliability}%
                    </div>
                    <div className="text-sm text-purple-600 font-medium">Reliability</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <Icons.Layers className="w-6 h-6 text-orange-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-900">
                      {incomeSources.length}
                    </div>
                    <div className="text-sm text-orange-600 font-medium">Income Sources</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Income Sources List */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-slate-900">Income Sources</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {incomeSources.map((source, index) => (
                <Card
                  key={source.id}
                  className="bg-white border-2 border-slate-200 hover:border-green-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                  onClick={() => {
                    setSelectedSourceId(source.id)
                    console.log('Edit income source:', source.id)
                  }}
                  style={{
                    animation: `slideInUp 0.6s ease-out ${index * 0.1}s both`
                  }}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-bold text-slate-900 truncate">
                        {source.name}
                      </CardTitle>
                      <Badge variant="outline" className={`${getStatusColor(source.status)} font-semibold`}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(source.status)}
                          <span className="capitalize">{source.status}</span>
                        </div>
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-500 capitalize">
                      {source.type.replace('_', ' ')} • {source.frequency}
                    </p>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Income Overview */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-green-50 rounded-xl">
                        <div className="text-lg font-bold text-green-600">
                          ${source.monthlyAmount.toLocaleString()}
                        </div>
                        <div className="text-xs text-green-600 font-medium">Monthly</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-xl">
                        <div className={`text-lg font-bold ${getGrowthColor(source.growth)}`}>
                          {source.growth > 0 ? '+' : ''}{source.growth}%
                        </div>
                        <div className="text-xs text-green-600 font-medium">Growth</div>
                      </div>
                    </div>

                    {/* Income Icon */}
                    <div className="flex items-center justify-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl flex items-center justify-center">
                        {getIncomeIcon(source.type)}
                      </div>
                    </div>

                    {/* Reliability */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-600">Reliability</span>
                        <span className={`font-semibold ${getReliabilityColor(source.reliability)}`}>
                          {source.reliability}%
                        </span>
                      </div>
                      <Progress value={source.reliability} className="h-2" />
                    </div>

                    {/* Quick Actions */}
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs"
                        onClick={(e) => {
                          e.stopPropagation()
                          console.log('Edit source:', source.id)
                        }}
                      >
                        <Icons.Edit className="w-3 h-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs"
                        onClick={(e) => {
                          e.stopPropagation()
                          console.log('View history for:', source.id)
                        }}
                      >
                        <Icons.BarChart3 className="w-3 h-3 mr-1" />
                        History
                      </Button>
                    </div>

                    {/* Quick Info */}
                    <div className="flex items-center justify-between text-sm text-slate-500">
                      <span>Next payment</span>
                      <span>{new Date(source.nextExpected).toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

        </div>
      </div>
    </AppLayout>
  )
}
