'use client'

import { AppLayout } from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function CategoriesPage() {
  return (
    <AppLayout>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="text-2xl mr-3">🏷️</span>
              Categories Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🚧</div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">Coming Soon</h2>
              <p className="text-gray-600 mb-4">
                Category management features are being developed.
              </p>
              <div className="text-left max-w-md mx-auto">
                <h3 className="font-semibold mb-2">Planned Features:</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• View All Categories</li>
                  <li>• Create/Edit Categories</li>
                  <li>• Sub-category Management</li>
                  <li>• Category Hierarchy</li>
                  <li>• Custom Icons & Colors</li>
                  <li>• Default Categories</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
