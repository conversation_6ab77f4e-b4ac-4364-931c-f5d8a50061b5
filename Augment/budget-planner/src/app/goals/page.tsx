'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { AppLayout } from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import * as Icons from 'lucide-react'

interface FinancialGoal {
  id: string
  name: string
  description: string
  targetAmount: number
  currentAmount: number
  deadline: string
  priority: 'High' | 'Medium' | 'Low'
  category: 'Security' | 'Transportation' | 'Travel' | 'Housing' | 'Investment' | 'Education' | 'Other'
  status: 'active' | 'completed' | 'paused'
  monthlyContribution: number
  estimatedCompletion: string
  color: string
  icon: string
}

// Mock data for professional design
const currentGoalsOverview = {
  totalGoals: 5,
  activeGoals: 4,
  completedGoals: 1,
  totalSaved: 44150.00,
  totalTarget: 108000.00,
  overallProgress: 41,
  monthlyContributions: 1850.00,
  period: 'November 1 - November 30, 2024'
}

const mockGoals: FinancialGoal[] = [
  {
    id: '1',
    name: 'Emergency Fund',
    description: 'Build a 6-month emergency fund for financial security',
    targetAmount: 15000,
    currentAmount: 12750,
    deadline: '2024-12-31',
    priority: 'High',
    category: 'Security',
    status: 'active',
    monthlyContribution: 500,
    estimatedCompletion: '2024-12-15',
    color: '#3B82F6',
    icon: 'shield'
  },
  {
    id: '2',
    name: 'New Car Fund',
    description: 'Save for a reliable used car',
    targetAmount: 25000,
    currentAmount: 15000,
    deadline: '2025-06-30',
    priority: 'Medium',
    category: 'Transportation',
    status: 'active',
    monthlyContribution: 600,
    estimatedCompletion: '2025-05-20',
    color: '#10B981',
    icon: 'car'
  },
  {
    id: '3',
    name: 'Europe Vacation',
    description: 'Dream vacation to Europe for 2 weeks',
    targetAmount: 8000,
    currentAmount: 6400,
    deadline: '2025-08-15',
    priority: 'Low',
    category: 'Travel',
    status: 'active',
    monthlyContribution: 300,
    estimatedCompletion: '2025-07-30',
    color: '#8B5CF6',
    icon: 'plane'
  },
  {
    id: '4',
    name: 'House Down Payment',
    description: 'Save 20% down payment for first home',
    targetAmount: 60000,
    currentAmount: 10000,
    deadline: '2026-12-31',
    priority: 'High',
    category: 'Housing',
    status: 'active',
    monthlyContribution: 1200,
    estimatedCompletion: '2026-11-15',
    color: '#F59E0B',
    icon: 'home'
  },
  {
    id: '5',
    name: 'Investment Portfolio',
    description: 'Build diversified investment portfolio',
    targetAmount: 50000,
    currentAmount: 50000,
    deadline: '2024-01-01',
    priority: 'Medium',
    category: 'Investment',
    status: 'completed',
    monthlyContribution: 0,
    estimatedCompletion: 'Completed',
    color: '#059669',
    icon: 'trending-up'
  }
]

export default function GoalsPage() {
  const router = useRouter()
  const [goals, setGoals] = useState<FinancialGoal[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedGoalId, setSelectedGoalId] = useState<string | null>(null)

  // Mock goals data - in production, this would come from API/database
  useEffect(() => {
    setTimeout(() => {
      setGoals(mockGoals)
      setLoading(false)
    }, 800)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-50 text-green-700 border-green-200'
      case 'completed': return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'paused': return 'bg-orange-50 text-orange-700 border-orange-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Icons.Play className="w-4 h-4" />
      case 'completed': return <Icons.CheckCircle className="w-4 h-4" />
      case 'paused': return <Icons.Pause className="w-4 h-4" />
      default: return <Icons.Circle className="w-4 h-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'bg-red-50 text-red-700 border-red-200'
      case 'Medium': return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'Low': return 'bg-green-50 text-green-700 border-green-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const getGoalIcon = (icon: string) => {
    switch (icon) {
      case 'shield': return <Icons.Shield className="w-6 h-6" />
      case 'car': return <Icons.Car className="w-6 h-6" />
      case 'plane': return <Icons.Plane className="w-6 h-6" />
      case 'home': return <Icons.Home className="w-6 h-6" />
      case 'trending-up': return <Icons.TrendingUp className="w-6 h-6" />
      default: return <Icons.Target className="w-6 h-6" />
    }
  }

  if (loading) {
    return (
      <AppLayout>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-purple-50/30">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-indigo-700 rounded-3xl flex items-center justify-center mx-auto animate-pulse">
              <Icons.Target className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-slate-900">Loading Goals...</h2>
            <p className="text-slate-600">Preparing your financial goals</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50/30 -m-6">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(139,92,246,0.15)_1px,transparent_0)] bg-[length:24px_24px] opacity-30 pointer-events-none"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">

          {/* Header Section */}
          <div className="space-y-8 mb-12">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-slate-500">
              <span className="text-slate-900 font-semibold">Financial Goals</span>
            </nav>

            {/* Main Header */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-6 lg:space-y-0">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-indigo-700 rounded-3xl flex items-center justify-center shadow-xl shadow-purple-600/25">
                    <Icons.Target className="w-8 h-8 text-white" strokeWidth={1.5} />
                  </div>
                  <div>
                    <h1 className="text-4xl lg:text-5xl font-bold text-gradient tracking-tight">
                      My Goals
                    </h1>
                    <p className="text-lg text-slate-600 font-medium">
                      Track and achieve your financial objectives
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center space-x-3">
                <Button
                  onClick={() => setShowAddModal(true)}
                  className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Icons.Plus className="w-5 h-5" />
                  <span>Add New Goal</span>
                </Button>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Icons.Download className="w-4 h-4" />
                  <span>Export</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Goals Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <Icons.Target className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-900">
                      {currentGoalsOverview.totalGoals}
                    </div>
                    <div className="text-sm text-purple-600 font-medium">Total Goals</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <Icons.DollarSign className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-900">
                      ${currentGoalsOverview.totalSaved.toLocaleString()}
                    </div>
                    <div className="text-sm text-green-600 font-medium">Total Saved</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Icons.TrendingUp className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-900">
                      {currentGoalsOverview.overallProgress}%
                    </div>
                    <div className="text-sm text-blue-600 font-medium">Overall Progress</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <Icons.Coins className="w-6 h-6 text-orange-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-900">
                      ${currentGoalsOverview.monthlyContributions.toLocaleString()}
                    </div>
                    <div className="text-sm text-orange-600 font-medium">Monthly Savings</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Goals List */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-slate-900">Financial Goals</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {goals.map((goal, index) => {
                const progress = Math.round((goal.currentAmount / goal.targetAmount) * 100)
                const remaining = goal.targetAmount - goal.currentAmount

                return (
                  <Card
                    key={goal.id}
                    className="bg-white border-2 border-slate-200 hover:border-purple-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                    onClick={() => {
                      setSelectedGoalId(goal.id)
                      console.log('Edit goal:', goal.id)
                    }}
                    style={{
                      animation: `slideInUp 0.6s ease-out ${index * 0.1}s both`
                    }}
                  >
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg font-bold text-slate-900 truncate">
                          {goal.name}
                        </CardTitle>
                        <Badge variant="outline" className={`${getStatusColor(goal.status)} font-semibold`}>
                          <div className="flex items-center space-x-1">
                            {getStatusIcon(goal.status)}
                            <span className="capitalize">{goal.status}</span>
                          </div>
                        </Badge>
                      </div>
                      <p className="text-sm text-slate-500">
                        {goal.description}
                      </p>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      {/* Goal Overview */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-purple-50 rounded-xl">
                          <div className="text-lg font-bold text-purple-600">
                            ${goal.currentAmount.toLocaleString()}
                          </div>
                          <div className="text-xs text-purple-600 font-medium">Current</div>
                        </div>
                        <div className="text-center p-3 bg-blue-50 rounded-xl">
                          <div className="text-lg font-bold text-blue-600">
                            ${remaining.toLocaleString()}
                          </div>
                          <div className="text-xs text-blue-600 font-medium">Remaining</div>
                        </div>
                      </div>

                      {/* Goal Icon */}
                      <div className="flex items-center justify-center">
                        <div
                          className="w-16 h-16 rounded-2xl flex items-center justify-center text-white"
                          style={{ backgroundColor: goal.color }}
                        >
                          {getGoalIcon(goal.icon)}
                        </div>
                      </div>

                      {/* Progress */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-600">Progress</span>
                          <span className="font-semibold text-purple-600">
                            {progress}%
                          </span>
                        </div>
                        <Progress value={progress} className="h-3" />
                      </div>

                      {/* Priority Badge */}
                      <div className="flex items-center justify-center">
                        <Badge variant="outline" className={`${getPriorityColor(goal.priority)} text-xs`}>
                          {goal.priority} Priority
                        </Badge>
                      </div>

                      {/* Quick Actions */}
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1 text-xs"
                          onClick={(e) => {
                            e.stopPropagation()
                            console.log('Add money to goal:', goal.id)
                          }}
                        >
                          <Icons.Plus className="w-3 h-3 mr-1" />
                          Add Money
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1 text-xs"
                          onClick={(e) => {
                            e.stopPropagation()
                            console.log('Edit goal:', goal.id)
                          }}
                        >
                          <Icons.Edit className="w-3 h-3 mr-1" />
                          Edit
                        </Button>
                      </div>

                      {/* Quick Info */}
                      <div className="flex items-center justify-between text-sm text-slate-500">
                        <span>Target date</span>
                        <span>{new Date(goal.deadline).toLocaleDateString()}</span>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

        </div>
      </div>
    </AppLayout>
  )
}
