'use client'

import { AppLayout } from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function SettingsPage() {
  return (
    <AppLayout>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="text-2xl mr-3">⚙️</span>
              Settings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🚧</div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">Coming Soon</h2>
              <p className="text-gray-600 mb-4">
                Settings and preferences are being developed.
              </p>
              <div className="text-left max-w-md mx-auto">
                <h3 className="font-semibold mb-2">Planned Features:</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Profile Settings</li>
                  <li>• Currency & Date Format</li>
                  <li>• Notification Preferences</li>
                  <li>• Security Settings</li>
                  <li>• Data Management</li>
                  <li>• Help & Support</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
