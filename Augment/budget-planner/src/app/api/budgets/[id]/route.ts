import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for budget updates
const updateBudgetSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  budgetCategories: z.array(z.object({
    id: z.string().optional(),
    categoryId: z.string(),
    categoryName: z.string(),
    amount: z.number(),
    percentage: z.number().optional(),
    color: z.string().optional(),
    description: z.string().optional()
  })).optional()
})

// GET /api/budgets/[id] - Get specific budget
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    // Use development user ID if no session (for development)
    const userId = session?.user?.id || (process.env.NODE_ENV === 'development' ? 'dev_user_123' : null)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params

    const budget = await prisma.budget.findFirst({
      where: {
        id: id,
        userId: userId
      },
      include: {
        budgetCategories: {
          include: {
            category: true
          }
        }
      }
    })

    if (!budget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      )
    }

    // Transform data to match frontend expectations
    const transformedBudget = {
      id: budget.id,
      name: budget.name,
      description: budget.description,
      startDate: budget.startDate.toISOString(),
      endDate: budget.endDate.toISOString(),
      totalMonthlyIncome: budget.budgetCategories.reduce((sum, cat) => sum + cat.amount, 0),
      totalAllocated: budget.budgetCategories.reduce((sum, cat) => sum + cat.amount, 0),
      remaining: 0, // TODO: Calculate from actual spending
      status: 'active', // TODO: Determine based on dates
      createdAt: budget.createdAt.toISOString(),
      incomeStreams: [], // TODO: Add income streams when implemented
      isCustom: false, // TODO: Determine from strategy
      categories: budget.budgetCategories.map(bc => ({
        id: bc.categoryId,
        name: bc.category.name,
        description: bc.category.description || `Budget category: ${bc.category.name}`,
        color: 'blue', // TODO: Store color in database or determine from category
        percentage: Math.round((bc.amount / budget.budgetCategories.reduce((sum, cat) => sum + cat.amount, 0)) * 100),
        allocated: bc.amount,
        spent: 0, // TODO: Calculate from transactions
        remaining: bc.amount, // TODO: Calculate remaining
        subcategories: [] // TODO: Add subcategories when implemented
      }))
    }

    return NextResponse.json(transformedBudget)

  } catch (error) {
    console.error('Error fetching budget:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/budgets/[id] - Update budget
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    // Use development user ID if no session (for development)
    const userId = session?.user?.id || (process.env.NODE_ENV === 'development' ? 'dev_user_123' : null)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateBudgetSchema.parse(body)

    // Check if budget exists and belongs to user
    const existingBudget = await prisma.budget.findFirst({
      where: {
        id: id,
        userId: userId
      }
    })

    if (!existingBudget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      )
    }

    // Update budget in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update budget basic info
      const updatedBudget = await tx.budget.update({
        where: { id: id },
        data: {
          ...(validatedData.name && { name: validatedData.name }),
          ...(validatedData.description && { description: validatedData.description }),
          ...(validatedData.startDate && { startDate: new Date(validatedData.startDate) }),
          ...(validatedData.endDate && { endDate: new Date(validatedData.endDate) })
        }
      })

      // Update budget categories if provided
      if (validatedData.budgetCategories) {
        // Delete existing budget categories
        await tx.budgetCategory.deleteMany({
          where: { budgetId: id }
        })

        // Create new budget categories
        for (const catData of validatedData.budgetCategories) {
          // Find or create category
          let category = await tx.category.findFirst({
            where: { name: catData.categoryName }
          })

          if (!category) {
            category = await tx.category.create({
              data: {
                name: catData.categoryName,
                description: catData.description
              }
            })
          }

          // Create budget category
          await tx.budgetCategory.create({
            data: {
              budgetId: id,
              categoryId: category.id,
              amount: catData.amount
            }
          })
        }
      }

      return updatedBudget
    })

    console.log('✅ Budget updated successfully:', result.id)

    return NextResponse.json({
      message: 'Budget updated successfully',
      budget: {
        id: result.id,
        name: result.name,
        description: result.description,
        startDate: result.startDate.toISOString(),
        endDate: result.endDate.toISOString(),
        updatedAt: result.updatedAt.toISOString()
      }
    })

  } catch (error) {
    console.error('Error updating budget:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/budgets/[id] - Delete budget
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    // Use development user ID if no session (for development)
    const userId = session?.user?.id || (process.env.NODE_ENV === 'development' ? 'dev_user_123' : null)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params

    // Check if budget exists and belongs to user
    const existingBudget = await prisma.budget.findFirst({
      where: {
        id: id,
        userId: userId
      }
    })

    if (!existingBudget) {
      return NextResponse.json(
        { error: 'Budget not found' },
        { status: 404 }
      )
    }

    // Delete budget (cascade will handle budget categories)
    await prisma.budget.delete({
      where: { id: id }
    })

    console.log('✅ Budget deleted successfully:', id)

    return NextResponse.json({
      message: 'Budget deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting budget:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
