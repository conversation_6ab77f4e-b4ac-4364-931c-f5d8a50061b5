import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schemas
const createBudgetSchema = z.object({
  name: z.string().min(1, 'Budget name is required'),
  description: z.string().optional(),
  startDate: z.string().refine((date) => {
    // Accept both date strings (YYYY-MM-DD) and datetime strings
    return !isNaN(Date.parse(date))
  }, 'Invalid date format'),
  endDate: z.string().refine((date) => {
    // Accept both date strings (YYYY-MM-DD) and datetime strings
    return !isNaN(Date.parse(date))
  }, 'Invalid date format'),
  totalIncome: z.number().positive('Total income must be positive'),
  strategy: z.string().optional(),
  selectedTemplate: z.any().optional(),
  incomeStreams: z.array(z.any()).optional(),
  budgetCategories: z.array(z.object({
    categoryId: z.string(),
    categoryName: z.string(),
    amount: z.number(),
    percentage: z.number(),
    spent: z.number().optional(),
    remaining: z.number().optional(),
    color: z.string().optional(),
    description: z.string().optional()
  }))
})

// GET /api/budgets - Get all budgets for authenticated user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Use development user ID if no session (for development)
    const userId = session?.user?.id || (process.env.NODE_ENV === 'development' ? 'dev_user_123' : null)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const budgets = await prisma.budget.findMany({
      where: {
        userId: userId
      },
      include: {
        budgetCategories: {
          include: {
            category: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Transform data to match frontend expectations
    const transformedBudgets = budgets.map(budget => ({
      id: budget.id,
      name: budget.name,
      description: budget.description,
      startDate: budget.startDate.toISOString(),
      endDate: budget.endDate.toISOString(),
      totalIncome: budget.budgetCategories.reduce((sum, cat) => sum + cat.amount, 0),
      totalAllocated: budget.budgetCategories.reduce((sum, cat) => sum + cat.amount, 0),
      totalSpent: 0, // TODO: Calculate from transactions
      status: 'active', // TODO: Determine based on dates
      createdAt: budget.createdAt.toISOString(),
      categories: budget.budgetCategories.length,
      budgetCategories: budget.budgetCategories.map(bc => ({
        id: bc.id,
        categoryId: bc.categoryId,
        categoryName: bc.category.name,
        amount: bc.amount,
        percentage: 0, // TODO: Calculate percentage
        spent: 0, // TODO: Calculate from transactions
        remaining: bc.amount // TODO: Calculate remaining
      }))
    }))

    return NextResponse.json({
      budgets: transformedBudgets,
      total: budgets.length
    })

  } catch (error) {
    console.error('Error fetching budgets:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/budgets - Create new budget
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Use development user ID if no session (for development)
    const userId = session?.user?.id || (process.env.NODE_ENV === 'development' ? 'dev_user_123' : null)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()

    console.log('📥 Received budget creation request:', {
      name: body.name,
      startDate: body.startDate,
      endDate: body.endDate,
      totalIncome: body.totalIncome,
      categoriesCount: body.budgetCategories?.length
    })

    // Validate request body
    const validatedData = createBudgetSchema.parse(body)

    // Create budget with categories in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the budget
      const budget = await tx.budget.create({
        data: {
          name: validatedData.name,
          description: validatedData.description,
          startDate: new Date(validatedData.startDate),
          endDate: new Date(validatedData.endDate),
          userId: userId
        }
      })

      // Create or find categories and create budget categories
      const budgetCategories = []
      
      for (const catData of validatedData.budgetCategories) {
        // Find or create category
        let category = await tx.category.findFirst({
          where: { name: catData.categoryName }
        })

        if (!category) {
          category = await tx.category.create({
            data: {
              name: catData.categoryName,
              description: catData.description
            }
          })
        }

        // Create budget category
        const budgetCategory = await tx.budgetCategory.create({
          data: {
            budgetId: budget.id,
            categoryId: category.id,
            amount: catData.amount
          }
        })

        budgetCategories.push({
          ...budgetCategory,
          category,
          categoryName: category.name,
          percentage: catData.percentage,
          spent: catData.spent || 0,
          remaining: catData.remaining || catData.amount,
          color: catData.color,
          description: catData.description
        })
      }

      return {
        ...budget,
        budgetCategories
      }
    })

    console.log('✅ Budget created successfully in database:', result.id)

    return NextResponse.json({
      message: 'Budget created successfully',
      budget: {
        id: result.id,
        name: result.name,
        description: result.description,
        startDate: result.startDate.toISOString(),
        endDate: result.endDate.toISOString(),
        createdAt: result.createdAt.toISOString(),
        budgetCategories: result.budgetCategories
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating budget:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
