'use client'

import { AppLayout } from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Bell, AlertTriangle, TrendingUp, Target, CreditCard, Settings, Check, X } from 'lucide-react'

// Sample notifications data
const notifications = [
  {
    id: 1,
    type: 'budget_alert',
    title: 'Budget Alert: Food & Dining',
    message: 'You\'ve spent 85% of your monthly food budget. Consider reducing dining out expenses.',
    timestamp: '2024-01-15T10:30:00Z',
    isRead: false,
    priority: 'high',
    icon: AlertTriangle,
    color: 'text-red-600'
  },
  {
    id: 2,
    type: 'goal_progress',
    title: 'Goal Milestone Reached!',
    message: 'Congratulations! You\'ve reached 50% of your Emergency Fund goal.',
    timestamp: '2024-01-14T15:45:00Z',
    isRead: false,
    priority: 'medium',
    icon: Target,
    color: 'text-green-600'
  },
  {
    id: 3,
    type: 'spending_insight',
    title: 'Spending Insight',
    message: 'Your transportation costs increased by 15% this month compared to last month.',
    timestamp: '2024-01-14T09:20:00Z',
    isRead: true,
    priority: 'low',
    icon: TrendingUp,
    color: 'text-blue-600'
  },
  {
    id: 4,
    type: 'bill_reminder',
    title: 'Bill Reminder',
    message: 'Your credit card payment of $450 is due in 3 days.',
    timestamp: '2024-01-13T14:00:00Z',
    isRead: true,
    priority: 'medium',
    icon: CreditCard,
    color: 'text-orange-600'
  },
  {
    id: 5,
    type: 'budget_alert',
    title: 'Monthly Budget Summary',
    message: 'Your monthly budget review is ready. You saved $200 more than expected!',
    timestamp: '2024-01-12T08:00:00Z',
    isRead: true,
    priority: 'low',
    icon: TrendingUp,
    color: 'text-green-600'
  }
]

const notificationSettings = [
  {
    id: 'budget_alerts',
    title: 'Budget Alerts',
    description: 'Get notified when you approach budget limits',
    enabled: true
  },
  {
    id: 'goal_updates',
    title: 'Goal Progress Updates',
    description: 'Receive updates on your financial goal milestones',
    enabled: true
  },
  {
    id: 'bill_reminders',
    title: 'Bill Reminders',
    description: 'Reminders for upcoming bill payments',
    enabled: true
  },
  {
    id: 'spending_insights',
    title: 'Spending Insights',
    description: 'Weekly insights about your spending patterns',
    enabled: false
  },
  {
    id: 'monthly_reports',
    title: 'Monthly Reports',
    description: 'Monthly financial summary and recommendations',
    enabled: true
  }
]

export default function NotificationsPage() {
  const unreadCount = notifications.filter(n => !n.isRead).length

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 48) return 'Yesterday'
    return date.toLocaleDateString()
  }

  return (
    <AppLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
            <p className="text-gray-600 mt-2">
              Stay updated with your financial activities
              {unreadCount > 0 && (
                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  {unreadCount} unread
                </span>
              )}
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Check className="w-4 h-4 mr-2" />
              Mark All Read
            </Button>
            <Button variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Notifications List */}
          <div className="lg:col-span-2 space-y-4">
            <h2 className="text-xl font-semibold text-gray-900">Recent Notifications</h2>
            
            <div className="space-y-3">
              {notifications.map((notification) => {
                const IconComponent = notification.icon
                
                return (
                  <Card key={notification.id} className={`hover:shadow-md transition-shadow duration-200 ${!notification.isRead ? 'border-blue-200 bg-blue-50/30' : ''}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-4">
                        <div className={`flex-shrink-0 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center ${notification.color}`}>
                          <IconComponent className="w-5 h-5" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h3 className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                              {notification.title}
                            </h3>
                            <div className="flex items-center space-x-2">
                              <Badge className={getPriorityColor(notification.priority)}>
                                {notification.priority}
                              </Badge>
                              {!notification.isRead && (
                                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                              )}
                            </div>
                          </div>
                          
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          
                          <div className="flex items-center justify-between mt-3">
                            <span className="text-xs text-gray-500">
                              {formatTimestamp(notification.timestamp)}
                            </span>
                            
                            <div className="flex space-x-2">
                              {!notification.isRead && (
                                <Button variant="ghost" size="sm" className="text-xs">
                                  Mark as read
                                </Button>
                              )}
                              <Button variant="ghost" size="sm" className="text-xs text-red-600">
                                <X className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* Notification Settings */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="w-5 h-5 mr-2" />
                  Notification Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {notificationSettings.map((setting) => (
                  <div key={setting.id} className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{setting.title}</h4>
                      <p className="text-xs text-gray-600">{setting.description}</p>
                    </div>
                    <Switch checked={setting.enabled} />
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Notification Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Total Notifications</span>
                  <span className="font-medium">{notifications.length}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Unread</span>
                  <span className="font-medium text-blue-600">{unreadCount}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">High Priority</span>
                  <span className="font-medium text-red-600">
                    {notifications.filter(n => n.priority === 'high').length}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
