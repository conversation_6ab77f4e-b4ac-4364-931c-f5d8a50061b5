'use client'

import { AppLayout } from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  HelpCircle, 
  MessageCircle, 
  Mail, 
  Phone, 
  Book, 
  Video, 
  Search,
  ChevronRight,
  Star,
  Clock,
  Users
} from 'lucide-react'

// FAQ Data
const faqs = [
  {
    id: 1,
    question: 'How do I create my first budget?',
    answer: 'Navigate to the Budget section and click "Create New Budget". Follow the 5-step wizard to set up your income, expenses, and financial goals.',
    category: 'Getting Started',
    popularity: 95
  },
  {
    id: 2,
    question: 'How can I track my expenses?',
    answer: 'Go to the Expenses section to manually add expenses or connect your bank account for automatic tracking. You can categorize expenses and view detailed analytics.',
    category: 'Expenses',
    popularity: 88
  },
  {
    id: 3,
    question: 'What are financial goals and how do I set them?',
    answer: 'Financial goals help you save for specific objectives like emergency funds, vacations, or major purchases. Visit the Goals section to create and track your progress.',
    category: 'Goals',
    popularity: 82
  },
  {
    id: 4,
    question: 'How do I connect my bank account?',
    answer: 'Currently, manual entry is supported. Bank integration features are coming soon. You can import CSV files from your bank statements.',
    category: 'Account',
    popularity: 76
  },
  {
    id: 5,
    question: 'Can I export my financial data?',
    answer: 'Yes! You can export your data in CSV or PDF format from the Settings page. This includes budgets, expenses, and goal progress reports.',
    category: 'Data',
    popularity: 71
  },
  {
    id: 6,
    question: 'How do I delete or modify a budget?',
    answer: 'In the Budget section, click on any existing budget to view, edit, or delete it. You can also create multiple budgets for different scenarios.',
    category: 'Budget',
    popularity: 68
  }
]

const helpCategories = [
  {
    title: 'Getting Started',
    description: 'Learn the basics of using Budget Planner',
    icon: Book,
    articles: 12,
    color: 'bg-blue-500'
  },
  {
    title: 'Budget Management',
    description: 'Create and manage your budgets effectively',
    icon: Users,
    articles: 8,
    color: 'bg-green-500'
  },
  {
    title: 'Expense Tracking',
    description: 'Track and categorize your expenses',
    icon: Clock,
    articles: 15,
    color: 'bg-purple-500'
  },
  {
    title: 'Goals & Savings',
    description: 'Set and achieve your financial goals',
    icon: Star,
    articles: 10,
    color: 'bg-orange-500'
  }
]

export default function HelpPage() {
  return (
    <AppLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Help & Support</h1>
          <p className="text-gray-600 mt-2 max-w-2xl mx-auto">
            Find answers to common questions, browse our knowledge base, or get in touch with our support team
          </p>
        </div>

        {/* Search Bar */}
        <Card className="max-w-2xl mx-auto">
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input 
                placeholder="Search for help articles, FAQs, or topics..." 
                className="pl-10 pr-4 py-3 text-lg"
              />
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <CardContent className="p-6 text-center">
              <MessageCircle className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Live Chat</h3>
              <p className="text-gray-600 text-sm mb-4">Get instant help from our support team</p>
              <Button className="w-full">Start Chat</Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <CardContent className="p-6 text-center">
              <Mail className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Email Support</h3>
              <p className="text-gray-600 text-sm mb-4">Send us a detailed message</p>
              <Button variant="outline" className="w-full">Send Email</Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
            <CardContent className="p-6 text-center">
              <Video className="w-12 h-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Video Tutorials</h3>
              <p className="text-gray-600 text-sm mb-4">Watch step-by-step guides</p>
              <Button variant="outline" className="w-full">Watch Videos</Button>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* FAQ Section */}
          <div className="lg:col-span-2 space-y-6">
            <h2 className="text-2xl font-semibold text-gray-900">Frequently Asked Questions</h2>
            
            <div className="space-y-4">
              {faqs.map((faq) => (
                <Card key={faq.id} className="hover:shadow-md transition-shadow duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-medium text-gray-900">{faq.question}</h3>
                          <Badge variant="outline" className="text-xs">
                            {faq.category}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-3">{faq.answer}</p>
                        <div className="flex items-center text-sm text-gray-500">
                          <Star className="w-4 h-4 mr-1" />
                          <span>{faq.popularity}% found this helpful</span>
                        </div>
                      </div>
                      <ChevronRight className="w-5 h-5 text-gray-400 ml-4" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Button variant="outline" className="w-full">
              View All FAQs
            </Button>
          </div>

          {/* Help Categories & Contact */}
          <div className="space-y-6">
            {/* Help Categories */}
            <Card>
              <CardHeader>
                <CardTitle>Browse by Category</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {helpCategories.map((category, index) => {
                  const IconComponent = category.icon
                  return (
                    <div key={index} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200">
                      <div className={`w-10 h-10 rounded-lg ${category.color} flex items-center justify-center`}>
                        <IconComponent className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{category.title}</h4>
                        <p className="text-sm text-gray-600">{category.articles} articles</p>
                      </div>
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    </div>
                  )
                })}
              </CardContent>
            </Card>

            {/* Contact Form */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Support</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                  <Input placeholder="Brief description of your issue" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
                  <Textarea 
                    placeholder="Describe your issue in detail..."
                    rows={4}
                  />
                </div>
                <Button className="w-full">
                  Send Message
                </Button>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <Card>
              <CardHeader>
                <CardTitle>Other Ways to Reach Us</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Mail className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="font-medium">Email</p>
                    <p className="text-sm text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="font-medium">Phone</p>
                    <p className="text-sm text-gray-600">1-800-BUDGET-1</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-gray-400" />
                  <div>
                    <p className="font-medium">Support Hours</p>
                    <p className="text-sm text-gray-600">Mon-Fri, 9AM-6PM EST</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
