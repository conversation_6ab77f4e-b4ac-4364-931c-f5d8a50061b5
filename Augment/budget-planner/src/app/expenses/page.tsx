'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { AppLayout } from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import * as Icons from 'lucide-react'
import { CSVImportModal } from '@/components/expenses/CSVImportModal'

interface Account {
  id: string
  name: string
  type: 'checking' | 'savings' | 'credit_card' | 'cash'
  balance: number
  transactions: number
  lastActivity: string
  status: 'active' | 'inactive' | 'pending'
}

interface ExpenseCategory {
  id: string
  name: string
  type: 'NEED' | 'WANT' | 'SMILE'
  budgeted: number
  spent: number
  remaining: number
  percentage: number
  color: string
  transactionCount: number
}

// Mock data for LF design
const currentExpenseOverview = {
  totalExpenses: 8750.00,
  thisMonth: 3247.85,
  lastMonth: 2890.45,
  averageDaily: 108.26,
  categorized: 156,
  uncategorized: 12,
  accounts: 4,
  period: 'November 1 - November 30, 2024'
}

const expenseCategories: ExpenseCategory[] = [
  {
    id: '1',
    name: 'NEED',
    type: 'NEED',
    budgeted: 2750.00,
    spent: 2100.45,
    remaining: 649.55,
    percentage: 76,
    color: 'blue',
    transactionCount: 45
  },
  {
    id: '2',
    name: 'WANTS',
    type: 'WANT',
    budgeted: 1650.00,
    spent: 847.40,
    remaining: 802.60,
    percentage: 51,
    color: 'orange',
    transactionCount: 32
  },
  {
    id: '3',
    name: 'SMILE',
    type: 'SMILE',
    budgeted: 1100.00,
    spent: 300.00,
    remaining: 800.00,
    percentage: 27,
    color: 'green',
    transactionCount: 8
  }
]

const mockAccounts: Account[] = [
  {
    id: '1',
    name: 'Chase Freedom Checking',
    type: 'checking',
    balance: 3247.82,
    transactions: 45,
    lastActivity: '2024-11-15',
    status: 'active'
  },
  {
    id: '2',
    name: 'Chase Sapphire Preferred',
    type: 'credit_card',
    balance: -1247.50,
    transactions: 32,
    lastActivity: '2024-11-14',
    status: 'active'
  },
  {
    id: '3',
    name: 'Wells Fargo Savings',
    type: 'savings',
    balance: 15420.00,
    transactions: 8,
    lastActivity: '2024-11-10',
    status: 'active'
  },
  {
    id: '4',
    name: 'Cash Wallet',
    type: 'cash',
    balance: 127.00,
    transactions: 12,
    lastActivity: '2024-11-15',
    status: 'active'
  }
]

export default function ExpensesPage() {
  const router = useRouter()
  const [accounts, setAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(true)
  const [showImportModal, setShowImportModal] = useState(false)
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(null)

  // Mock account data - in production, this would come from API/database
  useEffect(() => {
    setTimeout(() => {
      setAccounts(mockAccounts)
      setLoading(false)
    }, 800)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-50 text-green-700 border-green-200'
      case 'inactive': return 'bg-gray-50 text-gray-700 border-gray-200'
      case 'pending': return 'bg-orange-50 text-orange-700 border-orange-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Icons.CheckCircle className="w-4 h-4" />
      case 'inactive': return <Icons.Pause className="w-4 h-4" />
      case 'pending': return <Icons.Clock className="w-4 h-4" />
      default: return <Icons.Circle className="w-4 h-4" />
    }
  }

  const getAccountIcon = (type: string) => {
    switch (type) {
      case 'checking': return <Icons.Building2 className="w-6 h-6" />
      case 'savings': return <Icons.PiggyBank className="w-6 h-6" />
      case 'credit_card': return <Icons.CreditCard className="w-6 h-6" />
      case 'cash': return <Icons.Banknote className="w-6 h-6" />
      default: return <Icons.Wallet className="w-6 h-6" />
    }
  }

  if (loading) {
    return (
      <AppLayout>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-orange-50/30">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-600 to-amber-700 rounded-3xl flex items-center justify-center mx-auto animate-pulse">
              <Icons.CreditCard className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-slate-900">Loading Expenses...</h2>
            <p className="text-slate-600">Preparing your expense overview</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-orange-50/30 -m-6">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(249,115,22,0.15)_1px,transparent_0)] bg-[length:24px_24px] opacity-30 pointer-events-none"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">

          {/* Header Section */}
          <div className="space-y-8 mb-12">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-slate-500">
              <span className="text-slate-900 font-semibold">Expense Management</span>
            </nav>

            {/* Main Header */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-6 lg:space-y-0">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-orange-600 to-amber-700 rounded-3xl flex items-center justify-center shadow-xl shadow-orange-600/25">
                    <Icons.CreditCard className="w-8 h-8 text-white" strokeWidth={1.5} />
                  </div>
                  <div>
                    <h1 className="text-4xl lg:text-5xl font-bold text-gradient tracking-tight">
                      My Expenses
                    </h1>
                    <p className="text-lg text-slate-600 font-medium">
                      Track spending across all your accounts
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center space-x-3">
                <Button
                  onClick={() => setShowImportModal(true)}
                  className="flex items-center space-x-2 bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-700 hover:to-amber-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Icons.Upload className="w-5 h-5" />
                  <span>Import Transactions</span>
                </Button>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Icons.Download className="w-4 h-4" />
                  <span>Export</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Expense Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <Card className="bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <Icons.DollarSign className="w-6 h-6 text-orange-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-900">
                      ${currentExpenseOverview.totalExpenses.toLocaleString()}
                    </div>
                    <div className="text-sm text-orange-600 font-medium">Total Expenses</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Icons.Calendar className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-900">
                      ${currentExpenseOverview.thisMonth.toLocaleString()}
                    </div>
                    <div className="text-sm text-blue-600 font-medium">This Month</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <Icons.CheckCircle className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-900">
                      {currentExpenseOverview.categorized}
                    </div>
                    <div className="text-sm text-purple-600 font-medium">Categorized</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <Icons.Building2 className="w-6 h-6 text-orange-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-900">
                      {accounts.length}
                    </div>
                    <div className="text-sm text-orange-600 font-medium">Active Accounts</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Account List */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-slate-900">Financial Accounts</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {accounts.map((account, index) => (
                <Card
                  key={account.id}
                  className="bg-white border-2 border-slate-200 hover:border-orange-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                  onClick={() => {
                    setSelectedAccountId(account.id)
                    setShowImportModal(true)
                  }}
                  style={{
                    animation: `slideInUp 0.6s ease-out ${index * 0.1}s both`
                  }}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-bold text-slate-900 truncate">
                        {account.name}
                      </CardTitle>
                      <Badge variant="outline" className={`${getStatusColor(account.status)} font-semibold`}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(account.status)}
                          <span className="capitalize">{account.status}</span>
                        </div>
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-500 capitalize">
                      {account.type.replace('_', ' ')} Account
                    </p>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Account Overview */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-orange-50 rounded-xl">
                        <div className={`text-lg font-bold ${account.balance >= 0 ? 'text-orange-600' : 'text-red-600'}`}>
                          ${Math.abs(account.balance).toLocaleString()}
                        </div>
                        <div className="text-xs text-orange-600 font-medium">
                          {account.type === 'credit_card' ? 'Balance Owed' : 'Balance'}
                        </div>
                      </div>
                      <div className="text-center p-3 bg-blue-50 rounded-xl">
                        <div className="text-lg font-bold text-blue-600">
                          {account.transactions}
                        </div>
                        <div className="text-xs text-blue-600 font-medium">Transactions</div>
                      </div>
                    </div>

                    {/* Account Icon */}
                    <div className="flex items-center justify-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-amber-100 rounded-2xl flex items-center justify-center">
                        {getAccountIcon(account.type)}
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs"
                        onClick={(e) => {
                          e.stopPropagation()
                          setSelectedAccountId(account.id)
                          setShowImportModal(true)
                        }}
                      >
                        <Icons.Upload className="w-3 h-3 mr-1" />
                        Import CSV
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs"
                        onClick={(e) => {
                          e.stopPropagation()
                          console.log('View transactions for', account.id)
                        }}
                      >
                        <Icons.Eye className="w-3 h-3 mr-1" />
                        View
                      </Button>
                    </div>

                    {/* Quick Info */}
                    <div className="flex items-center justify-between text-sm text-slate-500">
                      <span>Last activity</span>
                      <span>{new Date(account.lastActivity).toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

        </div>
      </div>

      {/* CSV Import Modal */}
      <CSVImportModal
        isOpen={showImportModal}
        onClose={() => {
          setShowImportModal(false)
          setSelectedAccountId(null)
        }}
        onImportComplete={(newTransactions) => {
          console.log('Imported transactions:', newTransactions)
          setShowImportModal(false)
          setSelectedAccountId(null)
        }}
        selectedAccountId={selectedAccountId || undefined}
      />
    </AppLayout>
  )
}
