'use client'

import { AppLayout } from '@/components/layout/AppLayout'
import { IncomeCard } from '@/components/dashboard/IncomeCard'
import { ExpenseCard } from '@/components/dashboard/ExpenseCard'
import { BalanceCard } from '@/components/dashboard/BalanceCard'
import { ExpenseCategoryChart } from '@/components/dashboard/ExpenseCategoryChart'
import { GoalsWidget } from '@/components/dashboard/GoalsWidget'



export default function Dashboard() {
  return (
    <AppLayout>
      {/* 🎨 Enhanced Dashboard Container - World-Class Design */}
      <div className="min-h-screen -m-6 p-8"
           style={{
             background: 'linear-gradient(135deg, var(--color-gray-50) 0%, white 50%, var(--color-primary-50) 100%)'
           }}>
        {/* Subtle background pattern */}
        <div className="absolute inset-0 opacity-30 pointer-events-none"
             style={{
               backgroundImage: `radial-gradient(circle at 1px 1px, var(--color-primary-200) 1px, transparent 0)`,
               backgroundSize: '24px 24px'
             }}></div>

        <div className="relative max-w-7xl mx-auto space-y-10">
          {/* 📊 Enhanced Dashboard Header */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <h1 className="text-display-xl font-bold tracking-tight"
                    style={{ color: 'var(--color-gray-900)' }}>
                  Financial Dashboard
                </h1>
                <p className="text-body-lg font-medium"
                   style={{ color: 'var(--color-gray-600)' }}>
                  Track your financial health and achieve your goals
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-label-md font-medium"
                       style={{ color: 'var(--color-gray-500)' }}>
                    Last updated
                  </div>
                  <div className="text-body-md font-semibold"
                       style={{ color: 'var(--color-gray-900)' }}>
                    Just now
                  </div>
                </div>
                <div className="relative">
                  <div className="w-3 h-3 rounded-full animate-pulse"
                       style={{
                         backgroundColor: 'var(--color-success-400)',
                         boxShadow: '0 0 12px var(--color-success-400)'
                       }}></div>
                  <div className="absolute inset-0 w-3 h-3 rounded-full animate-ping"
                       style={{ backgroundColor: 'var(--color-success-400)' }}></div>
                </div>
              </div>
            </div>
          </div>

          {/* Top Row - Financial Overview Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <IncomeCard />
            <ExpenseCard />
            <GoalsWidget />
          </div>

          {/* Bottom Row - Analytics and Balance */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            <ExpenseCategoryChart />
            <BalanceCard />
          </div>

          {/* 💎 Enhanced Footer Insights */}
          <div className="relative overflow-hidden rounded-3xl p-8 border shadow-lg"
               style={{
                 background: 'linear-gradient(135deg, white 0%, var(--color-primary-50) 100%)',
                 borderColor: 'var(--color-primary-200)',
                 boxShadow: 'var(--shadow-lg)'
               }}>
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 opacity-10 pointer-events-none"
                 style={{
                   background: 'radial-gradient(circle, var(--color-primary-400) 0%, transparent 70%)',
                   transform: 'translate(25%, -25%)'
                 }}></div>

            <div className="relative text-center space-y-4">
              <h3 className="text-heading-lg font-semibold"
                  style={{ color: 'var(--color-gray-900)' }}>
                Financial Health Score
              </h3>
              <div className="flex items-center justify-center space-x-6">
                <div className="relative">
                  <div className="text-display-2xl font-bold"
                       style={{ color: 'var(--color-success-600)' }}>
                    87
                  </div>
                  {/* Score ring indicator */}
                  <div className="absolute -inset-4 rounded-full border-4 opacity-20 pointer-events-none"
                       style={{ borderColor: 'var(--color-success-500)' }}></div>
                </div>
                <div className="text-left space-y-1">
                  <div className="text-body-lg font-semibold"
                       style={{ color: 'var(--color-gray-900)' }}>
                    Excellent
                  </div>
                  <div className="text-label-md"
                       style={{ color: 'var(--color-gray-500)' }}>
                    Keep up the great work!
                  </div>
                  {/* Progress indicator */}
                  <div className="w-24 h-1 rounded-full overflow-hidden"
                       style={{ backgroundColor: 'var(--color-gray-200)' }}>
                    <div className="h-full rounded-full transition-all duration-1000"
                         style={{
                           backgroundColor: 'var(--color-success-500)',
                           width: '87%'
                         }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
