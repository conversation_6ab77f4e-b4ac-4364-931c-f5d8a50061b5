'use client'

import React, { useState, useEffect } from 'react'
import { AppLayout } from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import BudgetSuccessPage from '@/components/budget/BudgetSuccessPage'
import { useRouter } from 'next/navigation'
import EnhancedStrategySelection from '@/components/budget/EnhancedStrategySelection'
import EnhancedBudgetReview from '@/components/budget/EnhancedBudgetReview'

// Professional Icon System - Enterprise-Grade SVG Icons
const Icons = {
  // Step Icons
  Document: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  ),
  CurrencyDollar: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
    </svg>
  ),
  Template: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
    </svg>
  ),
  Category: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
    </svg>
  ),
  CheckCircle: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  Plus: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
    </svg>
  ),
  ArrowRight: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
    </svg>
  ),
  ArrowLeft: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 17l-5-5m0 0l5-5m-5 5h12" />
    </svg>
  ),
  Calendar: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
  ),
  Clock: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  Info: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  Shield: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  ),
  Refresh: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    </svg>
  ),
  Sparkles: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
    </svg>
  ),
  Rocket: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
    </svg>
  ),
  Check: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
  ),
  Briefcase: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8m0 0H5a2 2 0 00-2 2v6a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-3z" />
    </svg>
  ),
  Code: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
    </svg>
  ),
  TrendingUp: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
    </svg>
  ),
  Edit: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
    </svg>
  ),
  Trash: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
    </svg>
  ),
  X: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
  ),
  Home: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
    </svg>
  ),
  ShoppingCart: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
    </svg>
  ),
  // Additional icons for modal
  DollarSign: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
    </svg>
  ),
  Type: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
    </svg>
  ),
  AlertCircle: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  Building: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
    </svg>
  ),
  Tag: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
    </svg>
  ),
  FileText: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  ),
  Eye: ({ className = "w-6 h-6", ...props }) => (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
    </svg>
  )
}

// Advanced Form Validation System
const ValidationSystem = {
  // Validation rules
  rules: {
    required: (value, message = 'This field is required') =>
      !value || value.toString().trim() === '' ? message : null,

    minLength: (min, message) => (value) =>
      value && value.length < min ? message || `Minimum ${min} characters required` : null,

    maxLength: (max, message) => (value) =>
      value && value.length > max ? message || `Maximum ${max} characters allowed` : null,

    email: (value, message = 'Please enter a valid email address') =>
      value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? message : null,

    number: (value, message = 'Please enter a valid number') =>
      value && isNaN(Number(value)) ? message : null,

    min: (min, message) => (value) =>
      value && Number(value) < min ? message || `Minimum value is ${min}` : null,

    max: (max, message) => (value) =>
      value && Number(value) > max ? message || `Maximum value is ${max}` : null,

    percentage: (value, message = 'Please enter a value between 0 and 100') =>
      value && (Number(value) < 0 || Number(value) > 100) ? message : null,

    budgetTotal: (total, message = 'Budget allocation must equal 100%') =>
      total !== 100 ? message : null
  },

  // Validate field with multiple rules
  validateField: (value, rules) => {
    for (const rule of rules) {
      const error = typeof rule === 'function' ? rule(value) : rule
      if (error) return error
    }
    return null
  },

  // Validate entire form
  validateForm: (formData, validationSchema) => {
    const errors = {}
    let hasErrors = false

    Object.keys(validationSchema).forEach(field => {
      const error = ValidationSystem.validateField(formData[field], validationSchema[field])
      if (error) {
        errors[field] = error
        hasErrors = true
      }
    })

    return { errors, isValid: !hasErrors }
  }
}

// World-Class Design System & Animations
const designSystemStyles = `
  /* Enhanced Input Typography & Calendar Styling */
  input[type="text"], input[type="date"] {
    font-feature-settings: "tnum" 1, "kern" 1;
    letter-spacing: -0.01em;
  }

  input[type="date"]::-webkit-calendar-picker-indicator {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236366f1'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3e%3c/svg%3e");
    background-size: 20px 20px;
    background-repeat: no-repeat;
    background-position: center;
    width: 20px;
    height: 20px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s ease;
  }

  input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
  }

  input[type="date"]::-webkit-inner-spin-button,
  input[type="date"]::-webkit-clear-button {
    display: none;
  }

  /* Enhanced Tooltip Animation */
  .tooltip-enter {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
  }

  .tooltip-enter-active {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .tooltip-exit {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  .tooltip-exit-active {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
    transition: all 0.15s cubic-bezier(0.4, 0, 1, 1);
  }
  /* Premium Design Tokens */
  :root {
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-success-50: #ecfdf5;
    --color-success-500: #10b981;
    --color-warning-50: #fff7ed;
    --color-warning-500: #f97316;
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
  }

  /* Premium Slider Components */
  .slider-blue::-webkit-slider-thumb {
    appearance: none;
    height: 24px;
    width: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    cursor: pointer;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .slider-blue::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5), 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  .slider-orange::-webkit-slider-thumb {
    appearance: none;
    height: 24px;
    width: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    cursor: pointer;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .slider-orange::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(249, 115, 22, 0.5), 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  .slider-green::-webkit-slider-thumb {
    appearance: none;
    height: 24px;
    width: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    cursor: pointer;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .slider-green::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.5), 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  /* Premium Animation System */
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(24px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(-24px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.92) rotate(-1deg);
    }
    to {
      opacity: 1;
      transform: scale(1) rotate(0deg);
    }
  }

  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: scale(0.3) rotate(-5deg);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.08) rotate(1deg);
    }
    70% {
      transform: scale(0.95) rotate(-0.5deg);
    }
    100% {
      opacity: 1;
      transform: scale(1) rotate(0deg);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-4px);
    }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
    }
  }

  /* Advanced Validation Animations */
  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
    20%, 40%, 60%, 80% { transform: translateX(4px); }
  }

  @keyframes errorPulse {
    0% {
      border-color: rgb(239 68 68);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
    }
    50% {
      border-color: rgb(220 38 38);
      box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
    }
    100% {
      border-color: rgb(239 68 68);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
    }
  }

  @keyframes successPulse {
    0% {
      border-color: rgb(34 197 94);
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
    }
    50% {
      border-color: rgb(22 163 74);
      box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.1);
    }
    100% {
      border-color: rgb(34 197 94);
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
    }
  }

  @keyframes slideInError {
    from {
      opacity: 0;
      transform: translateY(-8px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes checkmark {
    0% {
      stroke-dasharray: 0 50;
      stroke-dashoffset: 0;
    }
    100% {
      stroke-dasharray: 50 50;
      stroke-dashoffset: -50;
    }
  }

  /* Premium Utility Classes */
  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    background-size: 200px 100%;
    animation: shimmer 2s infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .gradient-border {
    position: relative;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4) border-box;
    border: 2px solid transparent;
  }

  .text-gradient {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Advanced Validation Classes */
  .validation-error {
    animation: shake 0.5s ease-in-out, errorPulse 2s ease-in-out;
  }

  .validation-success {
    animation: successPulse 1s ease-in-out;
  }

  .error-message {
    animation: slideInError 0.3s ease-out;
  }

  .checkmark-animation {
    animation: checkmark 0.6s ease-in-out;
  }

  .field-shake {
    animation: shake 0.5s ease-in-out;
  }

  /* Enhanced Input States */
  .input-error {
    border-color: rgb(239 68 68) !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
    animation: errorPulse 2s ease-in-out;
  }

  .input-success {
    border-color: rgb(34 197 94) !important;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1) !important;
    animation: successPulse 1s ease-in-out;
  }

  .input-warning {
    border-color: rgb(245 158 11) !important;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1) !important;
  }
`

// Professional Budget Creation Steps
const STEPS = [
  { id: 1, name: 'Foundation', description: 'Budget name and period setup', icon: 'Document' },
  { id: 2, name: 'Income Sources', description: 'Define all revenue streams', icon: 'CurrencyDollar' },
  { id: 3, name: 'Strategy', description: 'Select allocation framework', icon: 'Template' },
  { id: 4, name: 'Categories', description: 'Organize spending buckets', icon: 'Category' },
  { id: 5, name: 'Review', description: 'Finalize and activate budget', icon: 'CheckCircle' }
]

// Mock income streams data
const mockIncomeStreams = [
  {
    id: '1',
    name: 'Primary Salary',
    amount: 4200,
    frequency: 'monthly',
    category: 'employment',
    isActive: true,
    monthlyAmount: 4200,
    annualAmount: 50400
  },
  {
    id: '2', 
    name: 'Freelance Work',
    amount: 12000,
    frequency: 'annually',
    category: 'business',
    isActive: true,
    monthlyAmount: 1000,
    annualAmount: 12000
  }
]

// Enhanced Budget Templates (Phase 2) - World-Class Strategy Selection
const budgetTemplates = [
  {
    id: '50-30-20',
    name: '50/30/20 Strategy',
    description: 'The gold standard for balanced budgeting',
    breakdown: { needs: 50, wants: 30, smile: 20 },
    categories: [
      { id: 'needs', name: 'NEEDS', percentage: 50, color: 'blue', description: 'Housing, groceries, utilities, transportation', editable: true },
      { id: 'wants', name: 'WANTS', percentage: 30, color: 'orange', description: 'Entertainment, dining, shopping, hobbies', editable: true },
      { id: 'smile', name: 'SMILE', percentage: 20, color: 'green', description: 'Savings, investments, debt payoff', editable: true }
    ],
    bestFor: 'Simplicity & balance',
    focusArea: 'Proportional spending',
    color: 'blue',
    icon: 'PieChart',
    popularity: 85,
    recommended: true,
    details: {
      needs: 'Essential expenses you cannot avoid - rent, groceries, utilities, minimum debt payments',
      wants: 'Things you enjoy but could live without - entertainment, dining out, hobbies',
      smile: 'Building your financial future - emergency fund, retirement, investments'
    }
  },
  {
    id: 'pay-yourself-first',
    name: 'Pay Yourself First',
    description: 'Prioritize your future before everything else',
    breakdown: { savings: 20, needs: 80 },
    categories: [
      { id: 'savings', name: 'SAVINGS', percentage: 20, color: 'green', description: 'Emergency fund, retirement, investments', editable: true },
      { id: 'needs', name: 'NEEDS', percentage: 80, color: 'blue', description: 'All essential and lifestyle expenses', editable: true }
    ],
    bestFor: 'Saving & investing automatically',
    focusArea: 'Prioritizing savings',
    color: 'green',
    icon: 'TrendingUp',
    popularity: 72,
    recommended: false,
    details: {
      savings: 'Automatically save first - emergency fund, retirement, investments, debt payoff',
      needs: 'Everything else - housing, food, transportation, entertainment, lifestyle'
    }
  },
  {
    id: 'custom',
    name: 'Custom Strategy',
    description: 'Build your personalized budget approach',
    breakdown: { custom: true },
    categories: [
      { id: 'needs', name: 'NEEDS', percentage: 50, color: 'blue', description: 'Essential expenses you cannot avoid', editable: true },
      { id: 'wants', name: 'WANTS', percentage: 30, color: 'orange', description: 'Things you enjoy but could live without', editable: true },
      { id: 'smile', name: 'SMILE', percentage: 20, color: 'green', description: 'Building your financial future', editable: true }
    ],
    bestFor: 'Advanced users & unique situations',
    focusArea: 'Personalized allocation',
    color: 'purple',
    icon: 'Settings',
    popularity: 43,
    recommended: false,
    isCustom: true,
    details: {
      needs: 'Your essential expenses - customize based on your lifestyle',
      wants: 'Your discretionary spending - adjust to your preferences',
      smile: 'Your savings goals - set your own financial targets'
    }
  }
]

// Category descriptions for better understanding
const categoryDescriptions = {
  needs: {
    title: 'NEEDS (Essential)',
    description: 'Must-have expenses you cannot avoid',
    examples: ['Rent/Mortgage', 'Groceries', 'Utilities', 'Transportation', 'Insurance', 'Minimum debt payments'],
    color: 'blue'
  },
  wants: {
    title: 'WANTS (Lifestyle)',
    description: 'Things you enjoy but could live without',
    examples: ['Entertainment', 'Dining out', 'Hobbies', 'Shopping', 'Subscriptions', 'Travel'],
    color: 'orange'
  },
  savings: {
    title: 'SMILE (Future)',
    description: 'Building your financial future and security',
    examples: ['Emergency fund', 'Retirement', 'Investments', 'Debt payoff', 'Major purchases'],
    color: 'green'
  }
}

export default function CreateBudgetPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1) // Start at step 1 for normal flow
  const [isBudgetFinalized, setIsBudgetFinalized] = useState(false)
  const [isCreatingBudget, setIsCreatingBudget] = useState(false)
  const [creationError, setCreationError] = useState<string | null>(null)
  const [createdBudgetId, setCreatedBudgetId] = useState<string | null>(null)

  const [budgetData, setBudgetData] = useState({
    name: '',
    description: '',
    startDate: '',
    endDate: '',
    incomeStreams: [],
    selectedTemplate: null,
    customCategories: [],
    totalMonthlyIncome: 0,
    totalAnnualIncome: 0,
    strategy: ''
  })

  const progressPercentage = (currentStep / STEPS.length) * 100

  // Handle budget creation with localStorage persistence
  const handleBudgetCreation = async () => {
    try {
      setIsCreatingBudget(true)
      setCreationError(null)

      console.log('🔄 Creating budget with localStorage persistence...')
      console.log('📊 Budget data:', budgetData)

      // Generate unique budget ID
      const budgetId = `budget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // Create budget categories from template
      const budgetCategories = []
      if (budgetData.selectedTemplate) {
        // Handle custom strategy with custom categories
        if (budgetData.selectedTemplate.isCustom && budgetData.selectedTemplate.customCategories) {
          console.log('🎨 Creating custom budget categories:', budgetData.selectedTemplate.customCategories)

          budgetData.selectedTemplate.customCategories.forEach((customCat, index) => {
            const amount = (budgetData.totalMonthlyIncome * customCat.percentage) / 100
            budgetCategories.push({
              id: `category_${Date.now()}_${index}`,
              categoryId: customCat.id,
              categoryName: customCat.name,
              amount: amount,
              percentage: customCat.percentage,
              spent: 0,
              remaining: amount,
              color: customCat.color,
              description: customCat.description
            })
          })
        }
        // Handle standard templates
        else if (budgetData.selectedTemplate.breakdown) {
          console.log('📊 Creating standard budget categories:', budgetData.selectedTemplate.breakdown)

          for (const [categoryKey, percentage] of Object.entries(budgetData.selectedTemplate.breakdown)) {
            let categoryName = categoryKey

            // Map template keys to actual category names
            if (categoryKey === 'needs') categoryName = 'Needs'
            else if (categoryKey === 'wants') categoryName = 'Wants'
            else if (categoryKey === 'savings' || categoryKey === 'smile') categoryName = 'Smile'

            const amount = (budgetData.totalMonthlyIncome * (percentage as number)) / 100
            budgetCategories.push({
              id: `category_${Date.now()}_${categoryName.toLowerCase()}`,
              categoryId: `cat_${categoryName.toLowerCase()}`,
              categoryName: categoryName,
              amount: amount,
              percentage: percentage as number,
              spent: 0,
              remaining: amount
            })
          }
        }
      }

      // Prepare budget data for API
      const budgetApiData = {
        name: budgetData.name,
        description: budgetData.description || `Budget created using ${budgetData.selectedTemplate?.name || 'custom'} strategy`,
        startDate: budgetData.startDate,
        endDate: budgetData.endDate,
        totalIncome: budgetData.totalMonthlyIncome,
        strategy: budgetData.strategy || budgetData.selectedTemplate?.id || 'custom',
        selectedTemplate: budgetData.selectedTemplate,
        incomeStreams: budgetData.incomeStreams || [],
        budgetCategories: budgetCategories
      }

      console.log('📤 Sending budget data to API:', budgetApiData)
      console.log('📅 Date formats - Start:', budgetData.startDate, 'End:', budgetData.endDate)

      // Import and use the API service
      const { budgetApi } = await import('@/lib/api/budgets')
      const createdBudget = await budgetApi.createBudget(budgetApiData)

      console.log('✅ Budget created successfully!')
      console.log('📊 Budget ID:', createdBudget.id)
      console.log('💰 Total Income:', budgetApiData.totalIncome)
      console.log('📋 Categories:', budgetCategories.length)
      console.log('💼 Income streams:', budgetApiData.incomeStreams.length)

      setCreatedBudgetId(createdBudget.id)
      setIsBudgetFinalized(true)

      // Show success message
      console.log('🎉 BUDGET CREATION COMPLETE!')
      console.log('✅ Budget saved to database!')

    } catch (error) {
      console.error('❌ Error creating budget:', error)
      setCreationError(error instanceof Error ? error.message : 'Failed to create budget')
    } finally {
      setIsCreatingBudget(false)
    }
  }

  return (
    <AppLayout>
      <style jsx>{designSystemStyles}</style>
      {/* Premium Create Budget Container */}
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 -m-6">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(59,130,246,0.15)_1px,transparent_0)] bg-[length:24px_24px] opacity-30 pointer-events-none"></div>

        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">

          {/* Premium Header with Progress */}
          <div className="space-y-8 mb-12">
            {/* Enhanced Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-slate-500" style={{ animation: 'slideInRight 0.6s ease-out' }}>
              <span className="hover:text-slate-700 transition-colors cursor-pointer">Budget Management</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-slate-900 font-semibold">Create New Budget</span>
            </nav>

            {/* Premium Main Header */}
            <div className="text-center space-y-6" style={{ animation: 'fadeInScale 0.8s ease-out 0.2s both' }}>
              <div className="space-y-4">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gradient tracking-tight">
                  Create Your Budget
                </h1>
                <p className="text-lg sm:text-xl text-slate-600 font-medium max-w-3xl mx-auto leading-relaxed">
                  Build a personalized financial plan that adapts to your lifestyle and helps you achieve your goals
                </p>
              </div>

              {/* Professional Trust Indicators */}
              <div className="flex items-center justify-center space-x-8 text-sm text-slate-500">
                <div className="flex items-center space-x-2">
                  <Icons.Shield className="w-4 h-4 text-emerald-500" strokeWidth={2} />
                  <span className="font-medium">Bank-level Security</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icons.Refresh className="w-4 h-4 text-blue-500" strokeWidth={2} />
                  <span className="font-medium">Real-time Sync</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icons.Sparkles className="w-4 h-4 text-purple-500" strokeWidth={2} />
                  <span className="font-medium">AI-Powered Insights</span>
                </div>
              </div>
            </div>

            {/* Premium Progress Indicator */}
            <div className="glass-effect rounded-3xl p-8 border border-white/20 shadow-xl" style={{ animation: 'slideInUp 0.8s ease-out 0.4s both' }}>
              <div className="flex items-center justify-between mb-6">
                <div className="space-y-1">
                  <h3 className="text-xl font-bold text-slate-900">
                    Step {currentStep} of {STEPS.length}
                  </h3>
                  <p className="text-sm text-slate-600">
                    {STEPS.find(s => s.id === currentStep)?.description}
                  </p>
                </div>
                <div className="text-right space-y-1">
                  <div className="text-2xl font-bold text-gradient">
                    {Math.round(progressPercentage)}%
                  </div>
                  <div className="text-xs text-slate-500 font-medium">Complete</div>
                </div>
              </div>

              {/* Enhanced Progress Bar */}
              <div className="relative mb-8">
                <div className="w-full bg-slate-200 rounded-full h-3 overflow-hidden">
                  <div
                    className="h-3 bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full transition-all duration-1000 ease-out relative"
                    style={{ width: `${progressPercentage}%` }}
                  >
                    <div className="absolute inset-0 bg-white/30 animate-shimmer"></div>
                  </div>
                </div>
              </div>

              {/* Premium Step Indicators */}
              <div className="relative">
                <div className="flex items-start justify-between">
                  {STEPS.map((step, index) => (
                    <div key={step.id} className="flex flex-col items-center space-y-3 flex-1 relative">
                      {/* Step Circle */}
                      <div className={`relative w-12 h-12 rounded-2xl flex items-center justify-center text-sm font-bold transition-all duration-500 ${
                        step.id <= currentStep
                          ? 'bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/30 animate-glow'
                          : step.id === currentStep + 1
                          ? 'bg-blue-100 text-blue-600 border-2 border-blue-200 animate-pulse'
                          : 'bg-slate-100 text-slate-400 border-2 border-slate-200'
                      }`}>
                        {step.id <= currentStep ? (
                          <Icons.Check className="w-5 h-5" strokeWidth={3} />
                        ) : (
                          step.id
                        )}

                        {/* Active Step Indicator */}
                        {step.id === currentStep && (
                          <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur opacity-75 animate-pulse"></div>
                        )}
                      </div>

                      {/* Step Info */}
                      <div className="text-center space-y-1">
                        <div className={`text-sm font-semibold transition-colors duration-300 ${
                          step.id <= currentStep ? 'text-slate-900' :
                          step.id === currentStep + 1 ? 'text-blue-600' : 'text-slate-500'
                        }`}>
                          {step.name}
                        </div>
                        <div className={`text-xs transition-colors duration-300 hidden sm:block ${
                          step.id <= currentStep ? 'text-slate-600' : 'text-slate-400'
                        }`}>
                          {step.description}
                        </div>
                      </div>

                      {/* Connection Line */}
                      {index < STEPS.length - 1 && (
                        <div className="absolute top-6 left-1/2 w-full h-0.5 -z-10">
                          <div className={`h-full transition-all duration-700 ${
                            step.id < currentStep
                              ? 'bg-gradient-to-r from-blue-500 to-purple-600'
                              : 'bg-slate-200'
                          }`} style={{ width: 'calc(100% - 3rem)', marginLeft: '1.5rem' }}></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Premium Step Content */}
          <div className="glass-effect rounded-3xl border border-white/20 shadow-2xl overflow-hidden" style={{ animation: 'slideInUp 0.8s ease-out 0.6s both' }}>
            <div className="gradient-border rounded-3xl">
              <div className="p-8 lg:p-12 bg-white/95 backdrop-blur-xl rounded-3xl">
              {currentStep === 1 && (
                <BudgetBasicsStep 
                  budgetData={budgetData} 
                  setBudgetData={setBudgetData}
                  onNext={() => setCurrentStep(2)}
                />
              )}
              {currentStep === 2 && (
                <IncomeStreamsStep 
                  budgetData={budgetData} 
                  setBudgetData={setBudgetData}
                  onNext={() => setCurrentStep(3)}
                  onBack={() => setCurrentStep(1)}
                />
              )}
              {currentStep === 3 && (
                <EnhancedStrategySelection
                  budgetData={budgetData}
                  setBudgetData={setBudgetData}
                  onNext={() => setCurrentStep(4)}
                  onBack={() => setCurrentStep(2)}
                />
              )}
              {currentStep === 4 && (
                <CategorySetupStep
                  budgetData={budgetData}
                  setBudgetData={setBudgetData}
                  onNext={() => setCurrentStep(5)}
                  onBack={() => setCurrentStep(3)}
                />
              )}
              {currentStep === 5 && !isBudgetFinalized && (
                <EnhancedBudgetReview
                  budgetData={budgetData}
                  onBack={() => setCurrentStep(4)}
                  onFinalize={async () => {
                    await handleBudgetCreation()
                  }}
                />
              )}
              {currentStep === 5 && isBudgetFinalized && (
                <BudgetSuccessPage
                  budgetData={budgetData}
                  createdBudgetId={createdBudgetId}
                  isCreating={isCreatingBudget}
                  creationError={creationError}
                  onViewBudget={() => {
                    // Navigate to budget management page
                    console.log('✅ Navigating to budget management...')
                    router.push('/budget')
                  }}
                  onCreateAnother={() => {
                    // Reset to create another budget
                    setCurrentStep(1)
                    setIsBudgetFinalized(false)
                    setCreatedBudgetId(null)
                    setCreationError(null)
                    setBudgetData({
                      name: '',
                      description: '',
                      startDate: '',
                      endDate: '',
                      incomeStreams: [],
                      selectedTemplate: null,
                      customCategories: [],
                      totalMonthlyIncome: 0,
                      totalAnnualIncome: 0,
                      strategy: ''
                    })
                  }}
                />
              )}
              </div>
            </div>
          </div>

        </div>
      </div>
    </AppLayout>
  )
}

// Step 1: Professional Budget Basics
function BudgetBasicsStep({ budgetData, setBudgetData, onNext }) {
  return (
    <div className="space-y-12">
      {/* Professional Header */}
      <div className="text-center space-y-6" style={{ animation: 'fadeInScale 0.6s ease-out' }}>
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-3xl shadow-xl shadow-blue-600/25 mb-6 animate-float">
          <Icons.Document className="w-10 h-10 text-white" strokeWidth={1.5} />
        </div>
        <div className="space-y-4">
          <h2 className="text-4xl lg:text-5xl font-bold text-gradient tracking-tight">
            Budget Foundation
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-medium">
            Establish the foundation of your financial plan with essential details that will guide your budgeting journey.
          </p>
        </div>

        {/* Professional Progress Indicator */}
        <div className="flex items-center justify-center space-x-3 text-sm text-slate-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="font-medium">Step 1 of 5</span>
          </div>
          <div className="w-1 h-1 bg-slate-300 rounded-full"></div>
          <span>Foundation Setup</span>
        </div>
      </div>

      <div className="max-w-4xl mx-auto space-y-10">
        {/* Optimized Budget Name Section */}
        <div className="space-y-6" style={{ animation: 'slideInUp 0.6s ease-out 0.2s both' }}>
          <div className="space-y-3">
            <label className="text-xl font-bold text-slate-900 flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                <Icons.Document className="w-4 h-4 text-white" strokeWidth={2} />
              </div>
              <span>Budget Name</span>
            </label>
            <p className="text-base text-slate-600 ml-11">
              Give your budget a clear, descriptive name that reflects your financial goals and time period.
            </p>
          </div>

          <div className="relative group ml-11">
            <Input
              placeholder="e.g., November 2024 Budget, Holiday Savings Plan, Q1 2025 Strategy"
              value={budgetData.name}
              onChange={(e) => setBudgetData({...budgetData, name: e.target.value})}
              className="h-16 text-xl font-semibold px-6 border-2 border-slate-200 rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-300 bg-white shadow-sm hover:shadow-md group-hover:border-slate-300 placeholder:text-slate-400 placeholder:font-normal"
            />
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>

            {/* Contextual Hint Tooltip */}
            <div className="absolute -top-2 right-4 opacity-0 group-focus-within:opacity-100 transition-all duration-300 transform translate-y-2 group-focus-within:translate-y-0">
              <div className="bg-slate-900 text-white text-xs px-3 py-2 rounded-lg shadow-lg">
                <div className="space-y-1">
                  <div className="font-semibold">💡 Best practices:</div>
                  <div>• Include time period</div>
                  <div>• Reference main goal</div>
                  <div>• Keep it memorable</div>
                </div>
                <div className="absolute bottom-0 left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-900 transform translate-y-full"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Optimized Date Range Section */}
        <div className="space-y-6" style={{ animation: 'slideInUp 0.6s ease-out 0.4s both' }}>
          <div className="space-y-3">
            <label className="text-xl font-bold text-slate-900 flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Icons.Calendar className="w-4 h-4 text-white" strokeWidth={2} />
              </div>
              <span>Budget Period</span>
            </label>
            <p className="text-base text-slate-600 ml-11">
              Define the time frame for your budget. Most users prefer monthly or quarterly periods.
            </p>
          </div>

          <div className="ml-11 grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Optimized Start Date */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <label className="text-lg font-semibold text-slate-900">Start Date</label>
              </div>
              <div className="relative group">
                <Input
                  type="date"
                  value={budgetData.startDate}
                  onChange={(e) => setBudgetData({...budgetData, startDate: e.target.value})}
                  className="h-16 text-lg font-semibold px-6 border-2 border-slate-200 rounded-2xl focus:border-emerald-500 focus:ring-4 focus:ring-emerald-500/20 transition-all duration-300 bg-white shadow-sm hover:shadow-md group-hover:border-slate-300 [&::-webkit-calendar-picker-indicator]:opacity-70 [&::-webkit-calendar-picker-indicator]:hover:opacity-100 [&::-webkit-calendar-picker-indicator]:cursor-pointer"
                  style={{ colorScheme: 'light' }}
                />
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500/5 to-green-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            {/* Optimized End Date */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gradient-to-br from-rose-500 to-red-600 rounded-lg flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <label className="text-lg font-semibold text-slate-900">End Date</label>
              </div>
              <div className="relative group">
                <Input
                  type="date"
                  value={budgetData.endDate}
                  onChange={(e) => setBudgetData({...budgetData, endDate: e.target.value})}
                  className="h-16 text-lg font-semibold px-6 border-2 border-slate-200 rounded-2xl focus:border-rose-500 focus:ring-4 focus:ring-rose-500/20 transition-all duration-300 bg-white shadow-sm hover:shadow-md group-hover:border-slate-300 [&::-webkit-calendar-picker-indicator]:opacity-70 [&::-webkit-calendar-picker-indicator]:hover:opacity-100 [&::-webkit-calendar-picker-indicator]:cursor-pointer"
                  style={{ colorScheme: 'light' }}
                />
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-rose-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>
          </div>

          {/* Optimized Period Summary - Clean & Minimal */}
          {budgetData.startDate && budgetData.endDate && (
            <div className="ml-11 bg-gradient-to-br from-slate-50 to-blue-50/50 rounded-3xl p-6 border border-slate-200/60 shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg shadow-blue-600/25">
                    <Icons.Clock className="w-5 h-5 text-white" strokeWidth={2} />
                  </div>
                  <div className="space-y-0.5">
                    <div className="text-base font-bold text-slate-900">
                      {Math.ceil((new Date(budgetData.endDate) - new Date(budgetData.startDate)) / (1000 * 60 * 60 * 24)) + 1} days
                    </div>
                    <div className="text-sm text-slate-600">
                      {Math.ceil((new Date(budgetData.endDate) - new Date(budgetData.startDate)) / (1000 * 60 * 60 * 24)) + 1 === 30 ||
                       Math.ceil((new Date(budgetData.endDate) - new Date(budgetData.startDate)) / (1000 * 60 * 60 * 24)) + 1 === 31 ?
                       'Monthly Budget' :
                       Math.ceil((new Date(budgetData.endDate) - new Date(budgetData.startDate)) / (1000 * 60 * 60 * 24)) + 1 >= 90 ?
                       'Quarterly Budget' : 'Custom Period'}
                    </div>
                  </div>
                </div>
                <div className="text-right text-sm text-slate-500">
                  {new Date(budgetData.startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {new Date(budgetData.endDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Duration Info */}
        {budgetData.startDate && budgetData.endDate && (
          <div className="bg-blue-50 rounded-xl p-4 border border-blue-200">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">📅</span>
              <div>
                <div className="text-body-md font-semibold text-blue-900">
                  Budget Duration: 1 Month
                </div>
                <div className="text-label-sm text-blue-700">
                  Perfect for monthly budget tracking
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Professional Navigation */}
      <div className="flex justify-end pt-12" style={{ animation: 'slideInUp 0.6s ease-out 0.8s both' }}>
        <Button
          onClick={onNext}
          disabled={!budgetData.name || !budgetData.startDate || !budgetData.endDate}
          className="group relative bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white px-12 py-5 rounded-2xl text-lg font-bold shadow-xl shadow-blue-600/25 hover:shadow-2xl hover:shadow-blue-600/30 transition-all duration-300 hover:scale-105 overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:shadow-lg"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-white/10 animate-shimmer pointer-events-none"></div>
          <div className="relative flex items-center space-x-4">
            <span>Continue to Income Setup</span>
            <div className="flex items-center justify-center w-6 h-6 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300">
              <Icons.ArrowRight className="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300" strokeWidth={2.5} />
            </div>
          </div>
        </Button>
      </div>
    </div>
  )
}

// Step 2: Professional Income Streams
function IncomeStreamsStep({ budgetData, setBudgetData, onNext, onBack }) {
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingStream, setEditingStream] = useState(null)

  const totalMonthly = budgetData.incomeStreams.reduce((sum, stream) => sum + stream.monthlyAmount, 0)
  const totalAnnual = budgetData.incomeStreams.reduce((sum, stream) => sum + stream.annualAmount, 0)

  // Professional income category icons
  const getIncomeIcon = (category) => {
    switch (category) {
      case 'employment': return 'Briefcase'
      case 'business': return 'Code'
      case 'investment': return 'TrendingUp'
      default: return 'CurrencyDollar'
    }
  }

  const handleFrequencyToggle = (streamId, newFrequency) => {
    const updatedStreams = budgetData.incomeStreams.map(stream => {
      if (stream.id === streamId) {
        const updatedStream = { ...stream, frequency: newFrequency }
        if (newFrequency === 'monthly') {
          updatedStream.monthlyAmount = stream.amount
          updatedStream.annualAmount = stream.amount * 12
        } else {
          updatedStream.annualAmount = stream.amount
          updatedStream.monthlyAmount = stream.amount / 12
        }
        return updatedStream
      }
      return stream
    })

    const newTotalMonthly = updatedStreams.reduce((sum, stream) => sum + stream.monthlyAmount, 0)
    const newTotalAnnual = updatedStreams.reduce((sum, stream) => sum + stream.annualAmount, 0)

    setBudgetData({
      ...budgetData,
      incomeStreams: updatedStreams,
      totalMonthlyIncome: newTotalMonthly,
      totalAnnualIncome: newTotalAnnual
    })
  }

  const handleEditStream = (stream) => {
    setEditingStream(stream)
    setShowAddModal(true)
  }

  const handleDeleteStream = (streamId) => {
    const updatedStreams = budgetData.incomeStreams.filter(stream => stream.id !== streamId)
    const newTotalMonthly = updatedStreams.reduce((sum, stream) => sum + stream.monthlyAmount, 0)
    const newTotalAnnual = updatedStreams.reduce((sum, stream) => sum + stream.annualAmount, 0)

    setBudgetData({
      ...budgetData,
      incomeStreams: updatedStreams,
      totalMonthlyIncome: newTotalMonthly,
      totalAnnualIncome: newTotalAnnual
    })
  }

  return (
    <div className="space-y-12">
      {/* Professional Header */}
      <div className="text-center space-y-6" style={{ animation: 'fadeInScale 0.6s ease-out' }}>
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-emerald-600 to-green-700 rounded-3xl shadow-xl shadow-emerald-600/25 mb-6 animate-float">
          <Icons.CurrencyDollar className="w-10 h-10 text-white" strokeWidth={1.5} />
        </div>
        <div className="space-y-4">
          <h2 className="text-4xl lg:text-5xl font-bold text-gradient tracking-tight">
            Income Sources
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-medium">
            Define all your revenue streams to create a comprehensive foundation for your budget planning.
          </p>
        </div>

        {/* Professional Progress Indicator */}
        <div className="flex items-center justify-center space-x-3 text-sm text-slate-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
            <span className="font-medium">Step 2 of 5</span>
          </div>
          <div className="w-1 h-1 bg-slate-300 rounded-full"></div>
          <span>Revenue Streams</span>
        </div>
      </div>

      {/* Professional Income Streams List */}
      <div className="max-w-5xl mx-auto space-y-6">
        {budgetData.incomeStreams.map((stream, index) => {
          const IconComponent = Icons[getIncomeIcon(stream.category)]
          return (
            <div
              key={stream.id}
              className="bg-white rounded-3xl p-8 border-2 border-slate-200 hover:border-emerald-300 hover:shadow-xl transition-all duration-300 group"
              style={{ animation: `slideInUp 0.6s ease-out ${index * 0.1}s both` }}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-6 flex-1">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                    <IconComponent className="w-8 h-8 text-white" strokeWidth={1.5} />
                  </div>
                  <div className="space-y-4 flex-1">
                    <div className="space-y-2">
                      <h3 className="text-2xl font-bold text-slate-900">{stream.name}</h3>
                      <div className="flex items-center space-x-3">
                        <span className="px-3 py-1 bg-emerald-100 text-emerald-700 rounded-xl text-sm font-medium capitalize">
                          {stream.category}
                        </span>
                        <span className="text-sm text-slate-500">•</span>
                        <span className="text-sm text-slate-600 font-medium">
                          {stream.frequency === 'monthly' ? 'Monthly Income' : 'Annual Income'}
                        </span>
                      </div>
                    </div>

                    {/* Professional Frequency Toggle */}
                    <div className="flex items-center space-x-4">
                      <span className="text-base font-semibold text-slate-700">Payment Frequency:</span>
                      <div className="flex bg-slate-100 rounded-2xl p-1.5 shadow-inner">
                        <button
                          onClick={() => handleFrequencyToggle(stream.id, 'monthly')}
                          className={`px-6 py-2 rounded-xl text-sm font-semibold transition-all duration-300 ${
                            stream.frequency === 'monthly'
                              ? 'bg-emerald-600 text-white shadow-lg shadow-emerald-600/25'
                              : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                          }`}
                        >
                          Monthly
                        </button>
                        <button
                          onClick={() => handleFrequencyToggle(stream.id, 'annually')}
                          className={`px-6 py-2 rounded-xl text-sm font-semibold transition-all duration-300 ${
                            stream.frequency === 'annually'
                              ? 'bg-emerald-600 text-white shadow-lg shadow-emerald-600/25'
                              : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                          }`}
                        >
                          Annually
                        </button>
                      </div>
                    </div>
                  </div>
              </div>

                {/* Professional Amount Display */}
                <div className="flex items-start space-x-6">
                  <div className="text-right space-y-2">
                    <div className="text-4xl font-bold text-slate-900">
                      ${stream.amount.toLocaleString()}
                    </div>
                    <div className="text-base text-emerald-600 font-semibold">
                      ${Math.round(stream.monthlyAmount).toLocaleString()}/month
                    </div>
                    <div className="text-sm text-slate-500">
                      ${Math.round(stream.annualAmount).toLocaleString()}/year
                    </div>
                  </div>

                  {/* Professional Action Buttons */}
                  <div className="flex items-center space-x-3 opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditStream(stream)}
                      className="h-12 w-12 p-0 hover:bg-blue-100 hover:text-blue-600 rounded-2xl transition-all duration-300 hover:scale-110"
                    >
                      <Icons.Edit className="w-5 h-5" strokeWidth={2} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteStream(stream.id)}
                      className="h-12 w-12 p-0 hover:bg-red-100 hover:text-red-600 rounded-2xl transition-all duration-300 hover:scale-110"
                    >
                      <Icons.Trash className="w-5 h-5" strokeWidth={2} />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )
        })}

        {/* Professional Add Income Stream Button */}
        <button
          onClick={() => {
            setEditingStream(null)
            setShowAddModal(true)
          }}
          className="w-full bg-gradient-to-br from-slate-50 to-emerald-50/50 border-2 border-dashed border-emerald-300 hover:border-emerald-400 rounded-3xl p-10 transition-all duration-300 hover:bg-emerald-50 hover:shadow-lg group"
          style={{ animation: 'slideInUp 0.6s ease-out 0.5s both' }}
        >
          <div className="flex flex-col items-center space-y-6">
            <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-green-600 group-hover:from-emerald-600 group-hover:to-green-700 rounded-3xl flex items-center justify-center transition-all duration-300 group-hover:scale-110 shadow-lg shadow-emerald-500/25">
              <Icons.Plus className="w-10 h-10 text-white group-hover:rotate-90 transition-transform duration-300" strokeWidth={2} />
            </div>
            <div className="text-center space-y-2">
              <div className="text-2xl font-bold text-slate-900 group-hover:text-emerald-700 transition-colors">
                Add Income Stream
              </div>
              <div className="text-base text-slate-600 max-w-md">
                Include salary, freelance work, investments, rental income, or any other revenue source
              </div>
            </div>
          </div>
        </button>
      </div>

      {/* Add/Edit Income Stream Modal */}
      {showAddModal && (
        <IncomeStreamModal
          stream={editingStream}
          onSave={(streamData) => {
            let updatedStreams
            if (editingStream) {
              // Edit existing stream
              updatedStreams = budgetData.incomeStreams.map(stream =>
                stream.id === editingStream.id ? { ...streamData, id: editingStream.id } : stream
              )
            } else {
              // Add new stream
              const newStream = {
                ...streamData,
                id: Date.now().toString()
              }
              updatedStreams = [...budgetData.incomeStreams, newStream]
            }

            // Update totals
            const newTotalMonthly = updatedStreams.reduce((sum, stream) => sum + stream.monthlyAmount, 0)
            const newTotalAnnual = updatedStreams.reduce((sum, stream) => sum + stream.annualAmount, 0)

            setBudgetData({
              ...budgetData,
              incomeStreams: updatedStreams,
              totalMonthlyIncome: newTotalMonthly,
              totalAnnualIncome: newTotalAnnual
            })
            setShowAddModal(false)
            setEditingStream(null)
          }}
          onCancel={() => {
            setShowAddModal(false)
            setEditingStream(null)
          }}
        />
      )}

      {/* Professional Total Income Summary */}
      <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-3xl p-8 border-2 border-emerald-200/60 shadow-lg" style={{ animation: 'slideInUp 0.6s ease-out 0.6s both' }}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-green-700 rounded-2xl flex items-center justify-center shadow-lg shadow-emerald-600/25">
              <Icons.CurrencyDollar className="w-8 h-8 text-white" strokeWidth={2} />
            </div>
            <div className="space-y-2">
              <h3 className="text-2xl font-bold text-emerald-900">Total Monthly Income</h3>
              <p className="text-base text-emerald-700 font-medium">
                {budgetData.incomeStreams.length} active income {budgetData.incomeStreams.length === 1 ? 'stream' : 'streams'}
              </p>
            </div>
          </div>
          <div className="text-right space-y-2">
            <div className="text-5xl font-bold text-gradient">
              ${totalMonthly.toLocaleString()}
            </div>
            <div className="text-lg text-emerald-700 font-semibold">
              ${totalAnnual.toLocaleString()} annually
            </div>
          </div>
        </div>

        {/* Income Breakdown */}
        {budgetData.incomeStreams.length > 1 && (
          <div className="mt-8 pt-6 border-t border-emerald-200/60">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-800">
                  {budgetData.incomeStreams.filter(s => s.category === 'employment').length}
                </div>
                <div className="text-sm text-emerald-600 font-medium">Employment</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-800">
                  {budgetData.incomeStreams.filter(s => s.category === 'business').length}
                </div>
                <div className="text-sm text-emerald-600 font-medium">Business</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-800">
                  {budgetData.incomeStreams.filter(s => s.category === 'investment').length}
                </div>
                <div className="text-sm text-emerald-600 font-medium">Investment</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Professional Navigation */}
      <div className="flex justify-between pt-12" style={{ animation: 'slideInUp 0.6s ease-out 0.8s both' }}>
        <Button
          variant="outline"
          onClick={onBack}
          className="group px-8 py-5 rounded-2xl text-lg font-semibold border-2 border-slate-300 hover:border-slate-400 hover:bg-slate-50 transition-all duration-300"
        >
          <div className="flex items-center space-x-3">
            <Icons.ArrowLeft className="w-5 h-5 group-hover:-translate-x-0.5 transition-transform duration-300" strokeWidth={2} />
            <span>Back to Foundation</span>
          </div>
        </Button>
        <Button
          onClick={onNext}
          disabled={budgetData.incomeStreams.length === 0}
          className="group relative bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 text-white px-12 py-5 rounded-2xl text-lg font-bold shadow-xl shadow-emerald-600/25 hover:shadow-2xl hover:shadow-emerald-600/30 transition-all duration-300 hover:scale-105 overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-white/10 animate-shimmer pointer-events-none"></div>
          <div className="relative flex items-center space-x-4">
            <span>Continue to Strategy</span>
            <div className="flex items-center justify-center w-6 h-6 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300">
              <Icons.ArrowRight className="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300" strokeWidth={2.5} />
            </div>
          </div>
        </Button>
      </div>
    </div>
  )
}

// Enhanced Validation Component
const ValidationMessage = ({ error, success, warning, className = "" }) => {
  if (!error && !success && !warning) return null

  const type = error ? 'error' : success ? 'success' : 'warning'
  const message = error || success || warning
  const icon = error ? Icons.AlertCircle : success ? Icons.CheckCircle : Icons.Info
  const IconComponent = icon

  return (
    <div className={`flex items-center space-x-2 text-sm font-medium error-message ${className}`}>
      <IconComponent
        className={`w-4 h-4 ${
          type === 'error' ? 'text-red-500' :
          type === 'success' ? 'text-green-500' :
          'text-amber-500'
        }`}
        strokeWidth={2}
      />
      <span className={
        type === 'error' ? 'text-red-600' :
        type === 'success' ? 'text-green-600' :
        'text-amber-600'
      }>
        {message}
      </span>
    </div>
  )
}

// Step 3: Professional Template Selection with Advanced Validation
function TemplateSelectionStep({ budgetData, setBudgetData, templates, onNext, onBack }) {
  const [selectedTemplate, setSelectedTemplate] = useState(budgetData.selectedTemplate)
  const [customAllocations, setCustomAllocations] = useState({
    needs: 50,
    wants: 30,
    savings: 20
  })
  const [showCustomDetails, setShowCustomDetails] = useState(false)
  const [customCategories, setCustomCategories] = useState([
    { id: 'needs', name: 'Essential Expenses', percentage: 50, color: 'blue', description: 'Must-have expenses you cannot avoid' },
    { id: 'wants', name: 'Lifestyle & Fun', percentage: 30, color: 'orange', description: 'Things you enjoy but could live without' },
    { id: 'savings', name: 'Future & Goals', percentage: 20, color: 'green', description: 'Building your financial future' }
  ])
  const [validationErrors, setValidationErrors] = useState({})
  const [isValidating, setIsValidating] = useState(false)

  // Professional template icons
  const getTemplateIcon = (templateId) => {
    switch (templateId) {
      case 'balanced': return 'Template'
      case 'conservative': return 'Shield'
      case 'aggressive': return 'TrendingUp'
      case 'custom': return 'Sparkles'
      default: return 'Template'
    }
  }

  // Advanced validation for template selection
  const validateTemplate = (template) => {
    const errors = {}

    if (!template) {
      errors.template = 'Please select a budget strategy'
      return errors
    }

    if (template.isCustom) {
      const total = getTotalPercentage()
      if (total !== 100) {
        errors.allocation = total > 100
          ? `Over budget by ${total - 100}%. Please reduce your allocations.`
          : `${100 - total}% remaining. Please allocate all your income.`
      }

      // Validate category names
      customCategories.forEach((cat, index) => {
        if (!cat.name.trim()) {
          errors[`category_${index}_name`] = 'Category name is required'
        }
        if (cat.percentage < 0 || cat.percentage > 100) {
          errors[`category_${index}_percentage`] = 'Percentage must be between 0 and 100'
        }
      })
    }

    return errors
  }

  const handleTemplateSelect = (template) => {
    setIsValidating(true)

    // Clear previous errors
    setValidationErrors({})

    if (template.isCustom) {
      setShowCustomDetails(true)
      // Initialize breakdown from custom categories
      const breakdown = {}
      customCategories.forEach(cat => {
        breakdown[cat.id] = cat.percentage
      })

      const updatedTemplate = {
        ...template,
        breakdown,
        customCategories
      }

      // Validate the template
      const errors = validateTemplate(updatedTemplate)
      setValidationErrors(errors)

      setSelectedTemplate(updatedTemplate)
      setBudgetData({...budgetData, selectedTemplate: updatedTemplate})
    } else {
      setShowCustomDetails(false)
      setSelectedTemplate(template)
      setBudgetData({...budgetData, selectedTemplate: template})
    }

    setTimeout(() => setIsValidating(false), 300)
  }

  const handleCustomAllocationChange = (category, value) => {
    const newAllocations = { ...customAllocations, [category]: value }

    // Ensure total doesn't exceed 100%
    const total = Object.values(newAllocations).reduce((sum, val) => sum + val, 0)
    if (total <= 100) {
      setCustomAllocations(newAllocations)

      if (selectedTemplate?.isCustom) {
        const updatedTemplate = {
          ...selectedTemplate,
          breakdown: newAllocations
        }
        setSelectedTemplate(updatedTemplate)
        setBudgetData({...budgetData, selectedTemplate: updatedTemplate})
      }
    }
  }

  const handleCustomCategoryChange = (categoryId, field, value) => {
    const updatedCategories = customCategories.map(cat =>
      cat.id === categoryId ? { ...cat, [field]: value } : cat
    )
    setCustomCategories(updatedCategories)

    // Update template breakdown if this is the selected template
    if (selectedTemplate?.isCustom) {
      const breakdown = {}
      updatedCategories.forEach(cat => {
        breakdown[cat.id] = cat.percentage
      })

      const updatedTemplate = {
        ...selectedTemplate,
        breakdown,
        customCategories: updatedCategories
      }
      setSelectedTemplate(updatedTemplate)
      setBudgetData({...budgetData, selectedTemplate: updatedTemplate})
    }
  }

  const addCustomCategory = () => {
    const newId = `custom_${Date.now()}`
    const newCategory = {
      id: newId,
      name: 'New Category',
      percentage: 0,
      color: 'purple',
      description: 'Add your description here'
    }
    setCustomCategories([...customCategories, newCategory])
  }

  const removeCustomCategory = (categoryId) => {
    if (customCategories.length > 1) {
      const updatedCategories = customCategories.filter(cat => cat.id !== categoryId)
      setCustomCategories(updatedCategories)

      // Update template if this is selected
      if (selectedTemplate?.isCustom) {
        const breakdown = {}
        updatedCategories.forEach(cat => {
          breakdown[cat.id] = cat.percentage
        })

        const updatedTemplate = {
          ...selectedTemplate,
          breakdown,
          customCategories: updatedCategories
        }
        setSelectedTemplate(updatedTemplate)
        setBudgetData({...budgetData, selectedTemplate: updatedTemplate})
      }
    }
  }

  const getTotalPercentage = () => {
    return customCategories.reduce((sum, cat) => sum + cat.percentage, 0)
  }

  return (
    <div className="space-y-12">
      {/* World-Class Header */}
      <div className="text-center space-y-8" style={{ animation: 'fadeInScale 0.6s ease-out' }}>
        <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-purple-600 to-indigo-700 rounded-3xl shadow-2xl shadow-purple-600/30 mb-8 animate-float">
          <Icons.Template className="w-12 h-12 text-white" strokeWidth={1.5} />
        </div>
        <div className="space-y-6">
          <h2 className="text-5xl lg:text-6xl font-bold text-gradient tracking-tight">
            Budget Strategy
          </h2>
          <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-medium">
            Choose a proven budgeting framework that aligns with your financial goals and lifestyle preferences.
            Each strategy is designed by financial experts to help you achieve different objectives.
          </p>
        </div>

        {/* Enhanced Progress Indicator */}
        <div className="flex items-center justify-center space-x-4 text-base text-slate-500">
          <div className="flex items-center space-x-3 bg-purple-50 px-4 py-2 rounded-xl border border-purple-200">
            <div className="w-3 h-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full shadow-sm"></div>
            <span className="font-semibold text-purple-700">Step 3 of 5</span>
          </div>
          <div className="w-2 h-2 bg-slate-300 rounded-full"></div>
          <span className="font-medium">Allocation Framework</span>
        </div>
      </div>

      {/* World-Class Template Cards */}
      <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8">
        {templates.map((template, index) => {
          const IconComponent = Icons[getTemplateIcon(template.id)]
          const isSelected = selectedTemplate?.id === template.id

          return (
            <div
              key={template.id}
              onClick={() => handleTemplateSelect(template)}
              className={`relative cursor-pointer rounded-3xl p-8 border-2 transition-all duration-500 hover:shadow-2xl group transform hover:-translate-y-2 ${
                isSelected
                  ? 'border-purple-500 bg-gradient-to-br from-purple-50 to-indigo-50 shadow-2xl shadow-purple-500/25 scale-105'
                  : 'border-slate-200 bg-white hover:border-purple-300 hover:bg-gradient-to-br hover:from-slate-50 hover:to-purple-50/30 hover:shadow-purple-500/10'
              }`}
              style={{
                animation: `slideInUp 0.6s ease-out ${index * 0.15}s both`,
                transformOrigin: 'center bottom'
              }}
            >
              {/* Enhanced Selection Indicator */}
              {isSelected && (
                <div className="absolute -top-2 -right-2 w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl shadow-purple-500/40 animate-pulse">
                  <Icons.Check className="w-6 h-6 text-white" strokeWidth={3} />
                </div>
              )}

              {/* Hover Glow Effect */}
              <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-purple-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>

              {/* Enhanced Template Content */}
              <div className="relative space-y-8">
                <div className="space-y-6">
                  <div className="relative">
                    <div className={`w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-3xl flex items-center justify-center shadow-xl shadow-purple-500/30 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 ${
                      isSelected ? 'animate-pulse' : ''
                    }`}>
                      <IconComponent className="w-10 h-10 text-white" strokeWidth={1.5} />
                    </div>
                    {/* Floating Badge */}
                    {template.popular && (
                      <div className="absolute -top-2 -right-2 bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg animate-bounce">
                        ⭐ Popular
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <h3 className="text-2xl font-bold text-slate-900 group-hover:text-purple-700 transition-colors duration-300">
                        {template.name}
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {template.isCustom && (
                          <Badge variant="outline" className="bg-gradient-to-r from-purple-50 to-indigo-50 text-purple-700 border-purple-200 text-sm font-semibold px-3 py-1">
                            🎨 Fully Customizable
                          </Badge>
                        )}
                        {template.recommended && (
                          <Badge variant="outline" className="bg-gradient-to-r from-emerald-50 to-green-50 text-emerald-700 border-emerald-200 text-sm font-semibold px-3 py-1">
                            ✨ Recommended
                          </Badge>
                        )}
                      </div>
                    </div>

                    <p className="text-base text-slate-600 leading-relaxed font-medium">{template.description}</p>
                  </div>
                </div>

                {/* Enhanced Allocation Breakdown */}
                <div className="space-y-6">
                  <h4 className="text-lg font-bold text-slate-900 flex items-center space-x-2">
                    <Icons.Category className="w-5 h-5 text-purple-600" strokeWidth={2} />
                    <span>Allocation Breakdown</span>
                  </h4>
                  <div className="space-y-5">
                    {/* NEEDS - Enhanced */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"></div>
                          <span className="text-sm font-bold text-blue-700">NEEDS</span>
                        </div>
                        <span className="text-lg font-bold text-blue-600 bg-blue-50 px-3 py-1 rounded-lg">
                          {template.isCustom && selectedTemplate?.id === template.id
                            ? customAllocations.needs
                            : template.breakdown.needs}%
                        </span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-4 overflow-hidden shadow-inner">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-blue-600 h-4 rounded-full transition-all duration-700 shadow-lg relative"
                          style={{
                            width: `${template.isCustom && selectedTemplate?.id === template.id
                              ? customAllocations.needs
                              : template.breakdown.needs}%`
                          }}
                        >
                          <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse"></div>
                        </div>
                      </div>
                    </div>

                    {/* WANTS - Enhanced */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full"></div>
                          <span className="text-sm font-bold text-orange-700">WANTS</span>
                        </div>
                        <span className="text-lg font-bold text-orange-600 bg-orange-50 px-3 py-1 rounded-lg">
                          {template.isCustom && selectedTemplate?.id === template.id
                            ? customAllocations.wants
                            : template.breakdown.wants}%
                        </span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-4 overflow-hidden shadow-inner">
                        <div
                          className="bg-gradient-to-r from-orange-500 to-orange-600 h-4 rounded-full transition-all duration-700 shadow-lg relative"
                          style={{
                            width: `${template.isCustom && selectedTemplate?.id === template.id
                              ? customAllocations.wants
                              : template.breakdown.wants}%`
                          }}
                        >
                          <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse"></div>
                        </div>
                      </div>
                    </div>

                    {/* SMILE - Enhanced */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-green-600 rounded-full"></div>
                          <span className="text-sm font-bold text-emerald-700">SMILE</span>
                        </div>
                        <span className="text-lg font-bold text-emerald-600 bg-emerald-50 px-3 py-1 rounded-lg">
                          {template.isCustom && selectedTemplate?.id === template.id
                            ? customAllocations.savings
                            : template.breakdown.savings}%
                        </span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-4 overflow-hidden shadow-inner">
                        <div
                          className="bg-gradient-to-r from-emerald-500 to-green-600 h-4 rounded-full transition-all duration-700 shadow-lg relative"
                          style={{
                            width: `${template.isCustom && selectedTemplate?.id === template.id
                              ? customAllocations.savings
                              : template.breakdown.savings}%`
                          }}
                        >
                          <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Enhanced Custom Allocation Controls */}
      {showCustomDetails && selectedTemplate?.isCustom && (
        <div className="bg-purple-50 rounded-2xl p-6 border border-purple-200">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-heading-md font-semibold text-purple-900">
              Create Your Custom Budget Strategy
            </h3>
            <Button
              onClick={addCustomCategory}
              variant="outline"
              className="bg-purple-100 border-purple-300 text-purple-700 hover:bg-purple-200"
            >
              + Add Category
            </Button>
          </div>

          <div className="space-y-6">
            {customCategories.map((category, index) => (
              <div key={category.id} className="bg-white rounded-xl p-5 border border-gray-200">
                <div className="space-y-4">
                  {/* Category Header with Name Input */}
                  <div className="flex items-center justify-between">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-3">
                        <Input
                          value={category.name}
                          onChange={(e) => handleCustomCategoryChange(category.id, 'name', e.target.value)}
                          className="text-body-md font-semibold border-0 bg-transparent p-0 focus:ring-0"
                          placeholder="Category Name"
                        />
                        {customCategories.length > 1 && (
                          <Button
                            onClick={() => removeCustomCategory(category.id)}
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            ✕
                          </Button>
                        )}
                      </div>
                      <Input
                        value={category.description}
                        onChange={(e) => handleCustomCategoryChange(category.id, 'description', e.target.value)}
                        className="text-label-sm text-gray-600 border-0 bg-transparent p-0 focus:ring-0"
                        placeholder="Describe what this category includes..."
                      />
                    </div>
                  </div>

                  {/* Percentage Control */}
                  <div className="flex items-center space-x-4">
                    <div className="flex-1">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={category.percentage}
                        onChange={(e) => handleCustomCategoryChange(category.id, 'percentage', parseInt(e.target.value))}
                        className={`w-full h-3 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-${category.color}`}
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0%</span>
                        <span>50%</span>
                        <span>100%</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        value={category.percentage}
                        onChange={(e) => handleCustomCategoryChange(category.id, 'percentage', Math.min(100, Math.max(0, parseInt(e.target.value) || 0)))}
                        className="w-16 text-center"
                      />
                      <span className="text-body-md font-bold text-gray-700">%</span>
                    </div>
                  </div>

                  {/* Dollar Amount Preview */}
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="text-center">
                      <div className={`text-display-md font-bold text-${category.color}-600`}>
                        ${Math.round((budgetData.totalMonthlyIncome * category.percentage) / 100).toLocaleString()}
                      </div>
                      <div className="text-label-sm text-gray-600">per month</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Advanced Total Validation with Animations */}
            <div className={`rounded-xl p-6 border-2 transition-all duration-500 ${
              getTotalPercentage() === 100
                ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 validation-success'
                : getTotalPercentage() > 100
                ? 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200 validation-error'
                : 'bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-200'
            }`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-xl flex items-center justify-center ${
                    getTotalPercentage() === 100 ? 'bg-green-500' :
                    getTotalPercentage() > 100 ? 'bg-red-500' : 'bg-amber-500'
                  }`}>
                    {getTotalPercentage() === 100 ? (
                      <Icons.CheckCircle className="w-5 h-5 text-white checkmark-animation" strokeWidth={2} />
                    ) : (
                      <Icons.AlertCircle className="w-5 h-5 text-white" strokeWidth={2} />
                    )}
                  </div>
                  <span className="text-lg font-bold text-slate-900">Total Allocation</span>
                </div>
                <span className={`text-2xl font-bold px-4 py-2 rounded-xl ${
                  getTotalPercentage() === 100 ? 'text-green-600 bg-green-100' :
                  getTotalPercentage() > 100 ? 'text-red-600 bg-red-100' : 'text-amber-600 bg-amber-100'
                }`}>
                  {getTotalPercentage()}%
                </span>
              </div>

              <div className="mb-4">
                <div className="w-full bg-slate-200 rounded-full h-4 overflow-hidden shadow-inner">
                  <div
                    className={`h-4 rounded-full transition-all duration-700 relative ${
                      getTotalPercentage() === 100 ? 'bg-gradient-to-r from-green-500 to-emerald-600' :
                      getTotalPercentage() > 100 ? 'bg-gradient-to-r from-red-500 to-rose-600' :
                      'bg-gradient-to-r from-amber-500 to-yellow-600'
                    }`}
                    style={{ width: `${Math.min(100, getTotalPercentage())}%` }}
                  >
                    <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>

              <ValidationMessage
                error={validationErrors.allocation}
                success={getTotalPercentage() === 100 ? "✓ Perfect! Your budget allocates 100% of your income." : null}
                warning={getTotalPercentage() < 100 && getTotalPercentage() > 0 ? `${100 - getTotalPercentage()}% remaining. Allocate all your income.` : null}
              />
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Template Preview */}
      {selectedTemplate && (
        <div className="bg-blue-50 rounded-2xl p-6 border border-blue-200">
          <h3 className="text-heading-md font-semibold text-blue-900 mb-6">
            Preview: {selectedTemplate.name}
          </h3>

          {/* Dynamic Category Preview */}
          {selectedTemplate.isCustom && selectedTemplate.customCategories ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                {selectedTemplate.customCategories.map((category) => (
                  <div key={category.id} className={`bg-${category.color}-50 rounded-xl p-4 border border-${category.color}-200`}>
                    <h4 className={`text-body-md font-semibold text-${category.color}-700 mb-2`}>
                      {category.name}
                    </h4>
                    <p className="text-label-sm text-gray-600 mb-3">{category.description}</p>
                    <div className={`text-display-md font-bold text-${category.color}-600`}>
                      ${Math.round((budgetData.totalMonthlyIncome * category.percentage) / 100).toLocaleString()}
                    </div>
                    <div className={`text-label-sm text-${category.color}-700`}>
                      {category.percentage}% of income
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <>
              {/* Standard Category Descriptions */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                {Object.entries(categoryDescriptions).map(([key, category]) => (
                  <div key={key} className={`bg-${category.color}-50 rounded-xl p-4 border border-${category.color}-200`}>
                    <h4 className={`text-body-md font-semibold text-${category.color}-700 mb-2`}>
                      {category.title}
                    </h4>
                    <p className="text-label-sm text-gray-600 mb-3">{category.description}</p>
                    <div className={`text-display-md font-bold text-${category.color}-600`}>
                      ${Math.round((budgetData.totalMonthlyIncome * selectedTemplate.breakdown[key]) / 100).toLocaleString()}
                    </div>
                    <div className={`text-label-sm text-${category.color}-700`}>
                      {selectedTemplate.breakdown[key]}% of income
                    </div>
                  </div>
                ))}
              </div>

              {/* Standard Detailed Breakdown */}
              <div className="bg-white rounded-xl p-4 border border-gray-200">
                <h4 className="text-body-md font-semibold text-gray-900 mb-3">Monthly Budget Breakdown</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-display-lg font-bold text-blue-600">
                      ${Math.round((budgetData.totalMonthlyIncome * selectedTemplate.breakdown.needs) / 100).toLocaleString()}
                    </div>
                    <div className="text-label-sm text-blue-700">NEEDS ({selectedTemplate.breakdown.needs}%)</div>
                  </div>
                  <div>
                    <div className="text-display-lg font-bold text-orange-600">
                      ${Math.round((budgetData.totalMonthlyIncome * selectedTemplate.breakdown.wants) / 100).toLocaleString()}
                    </div>
                    <div className="text-label-sm text-orange-700">WANTS ({selectedTemplate.breakdown.wants}%)</div>
                  </div>
                  <div>
                    <div className="text-display-lg font-bold text-green-600">
                      ${Math.round((budgetData.totalMonthlyIncome * selectedTemplate.breakdown.savings) / 100).toLocaleString()}
                    </div>
                    <div className="text-label-sm text-green-700">SMILE ({selectedTemplate.breakdown.savings}%)</div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      )}

      {/* Professional Navigation */}
      <div className="flex justify-between pt-12" style={{ animation: 'slideInUp 0.6s ease-out 0.8s both' }}>
        <Button
          variant="outline"
          onClick={onBack}
          className="group px-8 py-5 rounded-2xl text-lg font-semibold border-2 border-slate-300 hover:border-slate-400 hover:bg-slate-50 transition-all duration-300"
        >
          <div className="flex items-center space-x-3">
            <Icons.ArrowLeft className="w-5 h-5 group-hover:-translate-x-0.5 transition-transform duration-300" strokeWidth={2} />
            <span>Back to Income</span>
          </div>
        </Button>
        <Button
          onClick={onNext}
          disabled={
            !selectedTemplate ||
            (selectedTemplate?.isCustom && getTotalPercentage() !== 100)
          }
          className="group relative bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 hover:from-purple-700 hover:via-indigo-700 hover:to-blue-700 text-white px-12 py-5 rounded-2xl text-lg font-bold shadow-xl shadow-purple-600/25 hover:shadow-2xl hover:shadow-purple-600/30 transition-all duration-300 hover:scale-105 overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-white/10 animate-shimmer pointer-events-none"></div>
          <div className="relative flex items-center space-x-4">
            <span>Continue to Categories</span>
            <div className="flex items-center justify-center w-6 h-6 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300">
              <Icons.ArrowRight className="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300" strokeWidth={2.5} />
            </div>
          </div>
        </Button>
      </div>
    </div>
  )
}

// Step 4: Progressive Disclosure Wizard - World-Class Category Setup
function CategorySetupStep({ budgetData, setBudgetData, onNext, onBack }) {
  // Initialize categories based on selected template
  const initializeCategories = () => {
    if (!budgetData.selectedTemplate) return []

    const template = budgetData.selectedTemplate
    // Calculate monthly income from income streams if totalMonthlyIncome is not set
    const monthlyIncome = budgetData.totalMonthlyIncome ||
      (budgetData.incomeStreams?.reduce((sum, stream) => sum + (stream.monthlyAmount || 0), 0) || 0)

    // If still no income, use a default value to prevent NaN
    const safeMonthlyIncome = monthlyIncome || 5000

    // Handle custom strategy with custom categories
    if (template.isCustom && template.customCategories) {
      return template.customCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        color: cat.color,
        percentage: cat.percentage,
        totalBudget: Math.round((safeMonthlyIncome * cat.percentage) / 100),
        allocatedAmount: 0,
        subcategories: []
      }))
    }

    // Handle Pay Yourself First strategy
    if (template.id === 'pay-yourself-first') {
      return [
        {
          id: 'savings',
          name: 'SAVINGS',
          description: 'Emergency fund, retirement, investments',
          color: 'green',
          percentage: 20,
          totalBudget: Math.round((safeMonthlyIncome * 20) / 100),
          allocatedAmount: 0,
          subcategories: []
        },
        {
          id: 'needs',
          name: 'NEEDS',
          description: 'All essential and lifestyle expenses',
          color: 'blue',
          percentage: 80,
          totalBudget: Math.round((safeMonthlyIncome * 80) / 100),
          allocatedAmount: 0,
          subcategories: []
        }
      ]
    }

    // Default 50/30/20 strategy
    return [
      {
        id: 'needs',
        name: 'NEEDS',
        description: 'Essential expenses you cannot avoid',
        color: 'blue',
        percentage: template.breakdown?.needs || 50,
        totalBudget: Math.round((safeMonthlyIncome * (template.breakdown?.needs || 50)) / 100),
        allocatedAmount: 0,
        subcategories: []
      },
      {
        id: 'wants',
        name: 'WANTS',
        description: 'Things you enjoy but could live without',
        color: 'orange',
        percentage: template.breakdown?.wants || 30,
        totalBudget: Math.round((safeMonthlyIncome * (template.breakdown?.wants || 30)) / 100),
        allocatedAmount: 0,
        subcategories: []
      },
      {
        id: 'smile',
        name: 'SMILE',
        description: 'Building your financial future',
        color: 'green',
        percentage: template.breakdown?.smile || template.breakdown?.savings || 20,
        totalBudget: Math.round((safeMonthlyIncome * (template.breakdown?.smile || template.breakdown?.savings || 20)) / 100),
        allocatedAmount: 0,
        subcategories: []
      }
    ]
  }

  const [categories, setCategories] = useState(initializeCategories())
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [showAISuggestions, setShowAISuggestions] = useState(true)
  const [validationErrors, setValidationErrors] = useState({})

  // Initialize categories when component mounts or data changes
  useEffect(() => {
    const initialCategories = initializeCategories()
    if (initialCategories.length > 0) {
      setCategories(initialCategories)
    }
  }, [budgetData.selectedTemplate, budgetData.totalMonthlyIncome, budgetData.incomeStreams])

  // Professional category icons with enhanced mapping
  const getCategoryIcon = (categoryId) => {
    switch (categoryId) {
      case 'needs': return 'Home'
      case 'wants': return 'ShoppingCart'
      case 'savings': return 'TrendingUp'
      case 'smile': return 'TrendingUp'
      default:
        // For custom categories, try to map based on name or use default
        const category = categories.find(cat => cat.id === categoryId)
        if (category?.name?.toLowerCase().includes('saving')) return 'TrendingUp'
        if (category?.name?.toLowerCase().includes('need')) return 'Home'
        if (category?.name?.toLowerCase().includes('want')) return 'ShoppingCart'
        return 'Category'
    }
  }

  // Enhanced AI-Powered Subcategory Suggestions for All Categories
  const getAISuggestions = (categoryId, monthlyBudget) => {
    const suggestions = {
      needs: [
        { name: 'Rent/Mortgage', percentage: 60, icon: 'Home', description: 'Housing costs (rent, mortgage, property tax)' },
        { name: 'Groceries', percentage: 20, icon: 'ShoppingCart', description: 'Food and household essentials' },
        { name: 'Utilities', percentage: 10, icon: 'Zap', description: 'Electricity, water, gas, internet' },
        { name: 'Transportation', percentage: 10, icon: 'Car', description: 'Car payments, gas, public transport' }
      ],
      wants: [
        { name: 'Entertainment', percentage: 30, icon: 'Play', description: 'Movies, streaming, concerts, events' },
        { name: 'Dining Out', percentage: 25, icon: 'Coffee', description: 'Restaurants, takeout, coffee shops' },
        { name: 'Shopping', percentage: 25, icon: 'ShoppingBag', description: 'Clothes, electronics, non-essentials' },
        { name: 'Hobbies & Fitness', percentage: 20, icon: 'Heart', description: 'Gym, sports, hobbies, subscriptions' }
      ],
      savings: [
        { name: 'Emergency Fund', percentage: 40, icon: 'Shield', description: '3-6 months of expenses saved' },
        { name: 'Retirement', percentage: 30, icon: 'TrendingUp', description: '401k, IRA, pension contributions' },
        { name: 'Investments', percentage: 20, icon: 'DollarSign', description: 'Stocks, bonds, mutual funds' },
        { name: 'Debt Payoff', percentage: 10, icon: 'CreditCard', description: 'Extra payments on loans/credit cards' }
      ],
      smile: [
        { name: 'Emergency Fund', percentage: 40, icon: 'Shield', description: '3-6 months of expenses saved' },
        { name: 'Retirement', percentage: 30, icon: 'TrendingUp', description: '401k, IRA, pension contributions' },
        { name: 'Investments', percentage: 20, icon: 'DollarSign', description: 'Stocks, bonds, mutual funds' },
        { name: 'Debt Payoff', percentage: 10, icon: 'CreditCard', description: 'Extra payments on loans/credit cards' }
      ]
    }

    // For custom categories, provide generic suggestions based on category name
    let categoryKey = categoryId
    if (!suggestions[categoryId]) {
      const category = categories.find(cat => cat.id === categoryId)
      if (category?.name?.toLowerCase().includes('saving') || category?.name?.toLowerCase().includes('future')) {
        categoryKey = 'savings'
      } else if (category?.name?.toLowerCase().includes('need') || category?.name?.toLowerCase().includes('essential')) {
        categoryKey = 'needs'
      } else if (category?.name?.toLowerCase().includes('want') || category?.name?.toLowerCase().includes('lifestyle')) {
        categoryKey = 'wants'
      } else {
        // Default generic suggestions for unknown custom categories
        return [
          { name: 'Subcategory 1', percentage: 40, icon: 'Category', description: 'Add your first subcategory', budgetAmount: Math.round((monthlyBudget * 40) / 100) },
          { name: 'Subcategory 2', percentage: 35, icon: 'Category', description: 'Add your second subcategory', budgetAmount: Math.round((monthlyBudget * 35) / 100) },
          { name: 'Subcategory 3', percentage: 25, icon: 'Category', description: 'Add your third subcategory', budgetAmount: Math.round((monthlyBudget * 25) / 100) }
        ]
      }
    }

    return suggestions[categoryKey]?.map(suggestion => ({
      ...suggestion,
      budgetAmount: Math.round((monthlyBudget * suggestion.percentage) / 100)
    })) || []
  }

  // Progressive Navigation Functions
  const goToNextCategory = () => {
    if (currentCategoryIndex < categories.length - 1) {
      setIsTransitioning(true)
      setTimeout(() => {
        setCurrentCategoryIndex(currentCategoryIndex + 1)
        setIsTransitioning(false)
      }, 300)
    } else {
      // All categories completed, proceed to next step
      onNext()
    }
  }

  const goToPreviousCategory = () => {
    if (currentCategoryIndex > 0) {
      setIsTransitioning(true)
      setTimeout(() => {
        setCurrentCategoryIndex(currentCategoryIndex - 1)
        setIsTransitioning(false)
      }, 300)
    } else {
      // Go back to previous step
      onBack()
    }
  }

  // Apply AI Suggestions
  const applyAISuggestions = (categoryId) => {
    const category = categories.find(cat => cat.id === categoryId)
    if (!category) return

    const suggestions = getAISuggestions(categoryId, category.totalBudget)
    const updatedCategories = categories.map(cat => {
      if (cat.id === categoryId) {
        const newSubcategories = suggestions.map((suggestion, index) => ({
          id: `${categoryId}_ai_${index}`,
          name: suggestion.name,
          budgetAmount: suggestion.budgetAmount,
          isEditing: false,
          icon: suggestion.icon
        }))

        const allocatedAmount = newSubcategories.reduce((sum, sub) => sum + sub.budgetAmount, 0)

        return {
          ...cat,
          subcategories: newSubcategories,
          allocatedAmount
        }
      }
      return cat
    })

    setCategories(updatedCategories)
    setShowAISuggestions(false)
  }

  // Validate current category
  const validateCurrentCategory = () => {
    const currentCategory = categories[currentCategoryIndex]
    if (!currentCategory) return true

    const errors = {}

    // Check if category has subcategories
    if (currentCategory.subcategories.length === 0) {
      errors.subcategories = 'Please add at least one subcategory'
    }

    // Check if allocation is reasonable (at least 50% of budget used)
    const utilizationRate = (currentCategory.allocatedAmount / currentCategory.totalBudget) * 100
    if (utilizationRate < 50) {
      errors.utilization = `Only ${Math.round(utilizationRate)}% of budget allocated. Consider adding more subcategories.`
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Subcategory Management Functions
  const addSubcategory = (categoryId) => {
    const newSubcategory = {
      id: `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: '',
      budgetAmount: 0,
      isEditing: true,
      icon: 'Category'
    }

    const updatedCategories = categories.map(cat => {
      if (cat.id === categoryId) {
        return {
          ...cat,
          subcategories: [...(cat.subcategories || []), newSubcategory]
        }
      }
      return cat
    })

    setCategories(updatedCategories)
  }

  const removeSubcategory = (categoryId, subcategoryId) => {
    const updatedCategories = categories.map(cat => {
      if (cat.id === categoryId) {
        const updatedSubcategories = cat.subcategories.filter(sub => sub.id !== subcategoryId)
        const allocatedAmount = updatedSubcategories.reduce((sum, sub) => sum + (sub.budgetAmount || 0), 0)

        return {
          ...cat,
          subcategories: updatedSubcategories,
          allocatedAmount
        }
      }
      return cat
    })

    setCategories(updatedCategories)
  }

  const handleSubcategoryNameChange = (categoryId, subcategoryId, newName) => {
    const updatedCategories = categories.map(cat => {
      if (cat.id === categoryId) {
        const updatedSubcategories = cat.subcategories.map(sub => {
          if (sub.id === subcategoryId) {
            return { ...sub, name: newName }
          }
          return sub
        })

        return {
          ...cat,
          subcategories: updatedSubcategories
        }
      }
      return cat
    })

    setCategories(updatedCategories)
  }

  const handleSubcategoryBudgetChange = (categoryId, subcategoryId, newAmount) => {
    const amount = parseFloat(newAmount) || 0

    const updatedCategories = categories.map(cat => {
      if (cat.id === categoryId) {
        const updatedSubcategories = cat.subcategories.map(sub => {
          if (sub.id === subcategoryId) {
            return { ...sub, budgetAmount: amount }
          }
          return sub
        })

        const allocatedAmount = updatedSubcategories.reduce((sum, sub) => sum + (sub.budgetAmount || 0), 0)

        return {
          ...cat,
          subcategories: updatedSubcategories,
          allocatedAmount
        }
      }
      return cat
    })

    setCategories(updatedCategories)
  }

  const toggleSubcategoryEdit = (categoryId, subcategoryId) => {
    const updatedCategories = categories.map(cat => {
      if (cat.id === categoryId) {
        const updatedSubcategories = cat.subcategories.map(sub => {
          if (sub.id === subcategoryId) {
            return { ...sub, isEditing: !sub.isEditing }
          }
          return sub
        })

        return {
          ...cat,
          subcategories: updatedSubcategories
        }
      }
      return cat
    })

    setCategories(updatedCategories)
  }

  // Initialize categories based on selected template (REMOVED - using initializeCategories function instead)
  // This useEffect is now redundant since we use the initializeCategories function





  // Get current category for progressive disclosure
  const currentCategory = categories[currentCategoryIndex]
  const totalCategories = categories.length
  const progressPercentage = totalCategories > 0 ? ((currentCategoryIndex + 1) / totalCategories) * 100 : 0

  // Auto-show AI suggestions when navigating to a new category
  useEffect(() => {
    if (currentCategory && currentCategory.subcategories?.length === 0) {
      // Show AI suggestions automatically for empty categories
      setShowAISuggestions(true)
    } else {
      // Hide AI suggestions if category already has subcategories
      setShowAISuggestions(false)
    }
  }, [currentCategoryIndex, currentCategory])

  return (
    <div className="space-y-12">
      {/* World-Class Progressive Header */}
      <div className="text-center space-y-8" style={{ animation: 'fadeInScale 0.6s ease-out' }}>
        <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-orange-600 to-red-700 rounded-3xl shadow-2xl shadow-orange-600/30 mb-8 animate-float">
          <Icons.Category className="w-12 h-12 text-white" strokeWidth={1.5} />
        </div>

        <div className="space-y-6">
          <h2 className="text-5xl lg:text-6xl font-bold text-gradient tracking-tight">
            Category Setup
          </h2>
          <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-medium">
            Let's set up your budget categories one at a time. We'll guide you through each category to ensure
            your budget is comprehensive and realistic.
          </p>
        </div>

        {/* Enhanced Progress System */}
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Step Indicator */}
          <div className="flex items-center justify-center space-x-4 text-base text-slate-500">
            <div className="flex items-center space-x-3 bg-orange-50 px-4 py-2 rounded-xl border border-orange-200">
              <div className="w-3 h-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full shadow-sm"></div>
              <span className="font-semibold text-orange-700">Step 4 of 5</span>
            </div>
            <div className="w-2 h-2 bg-slate-300 rounded-full"></div>
            <span className="font-medium">Category Organization</span>
          </div>

          {/* Category Progress Bar */}
          {totalCategories > 0 && (
            <div className="space-y-3">
              <div className="flex justify-between items-center text-sm font-medium">
                <span className="text-slate-600">
                  Category {currentCategoryIndex + 1} of {totalCategories}
                </span>
                <span className="text-orange-600">
                  {Math.round(progressPercentage)}% Complete
                </span>
              </div>
              <div className="w-full bg-slate-200 rounded-full h-3 overflow-hidden shadow-inner">
                <div
                  className="bg-gradient-to-r from-orange-500 to-red-600 h-3 rounded-full transition-all duration-700 shadow-lg relative"
                  style={{ width: `${progressPercentage}%` }}
                >
                  <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          )}

          {/* Category Navigation Pills */}
          {totalCategories > 0 && (
            <div className="flex justify-center space-x-2">
              {categories.map((category, index) => {
                const isActive = index === currentCategoryIndex
                const isCompleted = index < currentCategoryIndex
                const IconComponent = Icons[getCategoryIcon(category.id)]

                return (
                  <button
                    key={category.id}
                    onClick={() => {
                      if (index <= currentCategoryIndex) {
                        setCurrentCategoryIndex(index)
                      }
                    }}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                      isActive
                        ? 'bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg scale-105'
                        : isCompleted
                        ? 'bg-green-100 text-green-700 hover:bg-green-200'
                        : 'bg-slate-100 text-slate-500 cursor-not-allowed'
                    }`}
                    disabled={index > currentCategoryIndex}
                  >
                    {isCompleted ? (
                      <Icons.CheckCircle className="w-4 h-4" strokeWidth={2} />
                    ) : (
                      <IconComponent className="w-4 h-4" strokeWidth={2} />
                    )}
                    <span className="text-sm font-semibold">{category.name}</span>
                  </button>
                )
              })}
            </div>
          )}
        </div>
      </div>

      {/* Progressive Disclosure: Single Category Focus */}
      {currentCategory && (
        <div className="max-w-4xl mx-auto space-y-8" style={{ animation: isTransitioning ? 'fadeOut 0.3s ease-out' : 'fadeInScale 0.6s ease-out 0.2s both' }}>

          {/* Current Category Header */}
          <div className="bg-gradient-to-br from-slate-50 to-blue-50/50 rounded-3xl p-8 border-2 border-slate-200/60 shadow-lg">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className={`w-16 h-16 bg-gradient-to-br ${
                  currentCategory.color === 'blue' ? 'from-blue-500 to-blue-600' :
                  currentCategory.color === 'orange' ? 'from-orange-500 to-orange-600' :
                  'from-emerald-500 to-green-600'
                } rounded-2xl flex items-center justify-center shadow-lg`}>
                  {React.createElement(Icons[getCategoryIcon(currentCategory.id)], {
                    className: "w-8 h-8 text-white",
                    strokeWidth: 1.5
                  })}
                </div>
                <div className="space-y-2">
                  <h3 className="text-3xl font-bold text-slate-900">{currentCategory.name}</h3>
                  <p className="text-lg text-slate-600 leading-relaxed">{currentCategory.description}</p>
                </div>
              </div>

              <div className="text-right space-y-2">
                <div className="text-4xl font-bold text-gradient">
                  ${currentCategory.totalBudget?.toLocaleString()}
                </div>
                <div className="text-sm text-slate-500 font-medium">
                  {currentCategory.percentage}% of monthly income
                </div>
              </div>
            </div>

            {/* Enhanced Progress Bar for Current Category */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-slate-700">Budget Allocation</span>
                <span className={`text-lg font-bold ${
                  currentCategory.allocatedAmount === currentCategory.totalBudget ? 'text-green-600' :
                  currentCategory.allocatedAmount > currentCategory.totalBudget ? 'text-red-600' :
                  'text-amber-600'
                }`}>
                  ${currentCategory.allocatedAmount?.toLocaleString()} / ${currentCategory.totalBudget?.toLocaleString()}
                </span>
              </div>

              <div className="w-full bg-slate-200 rounded-full h-4 overflow-hidden shadow-inner">
                <div
                  className={`h-4 rounded-full transition-all duration-1000 ease-out shadow-sm relative ${
                    currentCategory.color === 'blue' ? 'bg-gradient-to-r from-blue-500 to-blue-600' :
                    currentCategory.color === 'orange' ? 'bg-gradient-to-r from-orange-500 to-orange-600' :
                    'bg-gradient-to-r from-emerald-500 to-green-600'
                  }`}
                  style={{
                    width: `${Math.min(100, (currentCategory.allocatedAmount / currentCategory.totalBudget) * 100)}%`
                  }}
                >
                  <div className="absolute inset-0 bg-white/30 rounded-full animate-shimmer"></div>
                </div>
              </div>

              <div className="flex justify-between text-sm font-medium">
                <span className="text-emerald-600">
                  ${currentCategory.allocatedAmount?.toLocaleString()} allocated
                </span>
                <span className="text-slate-500">
                  ${(currentCategory.totalBudget - currentCategory.allocatedAmount)?.toLocaleString()} remaining
                </span>
              </div>
            </div>
          </div>

          {/* AI Suggestions Panel */}
          {showAISuggestions && (
            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border-2 border-purple-200 shadow-lg" style={{ animation: 'slideInUp 0.6s ease-out 0.4s both' }}>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
                    <Icons.Sparkles className="w-5 h-5 text-white" strokeWidth={2} />
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-purple-900">AI Budget Assistant</h4>
                    <p className="text-sm text-purple-700">Smart suggestions based on your income and category</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowAISuggestions(false)}
                  className="text-purple-500 hover:text-purple-700 transition-colors duration-200"
                >
                  <Icons.X className="w-5 h-5" strokeWidth={2} />
                </button>
              </div>

              <div className="space-y-4">
                <p className="text-purple-800 font-medium">
                  💡 Based on your ${currentCategory.totalBudget?.toLocaleString()} {currentCategory.name.toLowerCase()} budget,
                  here are some smart allocations:
                </p>

                <div className="grid grid-cols-1 gap-4">
                  {getAISuggestions(currentCategory.id, currentCategory.totalBudget).map((suggestion, index) => (
                    <div key={index} className="bg-white rounded-xl p-5 border border-purple-200 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          {React.createElement(Icons[suggestion.icon] || Icons.Category, {
                            className: "w-6 h-6 text-purple-600",
                            strokeWidth: 2
                          })}
                          <div>
                            <span className="font-semibold text-slate-900 text-lg">{suggestion.name}</span>
                            <p className="text-sm text-slate-600 mt-1">{suggestion.description}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="text-xl font-bold text-purple-600">
                            ${suggestion.budgetAmount?.toLocaleString()}
                          </span>
                          <p className="text-sm text-purple-500 font-medium">
                            {suggestion.percentage}% of category
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex justify-center space-x-4 pt-4">
                  <button
                    onClick={() => applyAISuggestions(currentCategory.id)}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    ✨ Apply All Suggestions
                  </button>
                  <button
                    onClick={() => setShowAISuggestions(false)}
                    className="px-6 py-3 bg-white text-purple-600 font-semibold rounded-xl border-2 border-purple-200 hover:bg-purple-50 transition-all duration-300"
                  >
                    I'll Set Up Manually
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Subcategory Management - Infinite Scroll Design */}
          <div className="bg-white rounded-3xl p-8 border-2 border-slate-200 shadow-lg">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-slate-600 to-slate-700 rounded-xl flex items-center justify-center">
                  <Icons.Category className="w-5 h-5 text-white" strokeWidth={2} />
                </div>
                <div>
                  <h4 className="text-2xl font-bold text-slate-900">Subcategories</h4>
                  <p className="text-sm text-slate-600">Break down your {currentCategory.name.toLowerCase()} into specific areas</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowAISuggestions(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <Icons.Sparkles className="w-4 h-4" strokeWidth={2} />
                  <span>AI Suggestions</span>
                </button>
                <button
                  onClick={() => addSubcategory(currentCategory.id)}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <Icons.Plus className="w-4 h-4" strokeWidth={2} />
                  <span>Add Subcategory</span>
                </button>
              </div>
            </div>

            {/* Subcategories List - Optimized for 20+ items */}
            <div className="space-y-4 max-h-96 overflow-y-auto pr-2" style={{ scrollbarWidth: 'thin' }}>
              {currentCategory.subcategories?.length === 0 ? (
                <div className="text-center py-12 space-y-4">
                  <div className="w-16 h-16 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto">
                    <Icons.Plus className="w-8 h-8 text-slate-400" strokeWidth={1.5} />
                  </div>
                  <div className="space-y-2">
                    <h5 className="text-lg font-semibold text-slate-700">No subcategories yet</h5>
                    <p className="text-slate-500">Add subcategories to organize your {currentCategory.name.toLowerCase()} budget</p>
                  </div>
                </div>
              ) : (
                currentCategory.subcategories?.map((subcategory, subIndex) => (
                  <div
                    key={subcategory.id}
                    className="bg-slate-50 rounded-2xl p-6 border-2 border-slate-200 hover:border-slate-300 hover:shadow-md transition-all duration-300 group"
                    style={{
                      animation: `slideInUp 0.4s ease-out ${subIndex * 0.05}s both`
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 mr-4">
                        {subcategory.isEditing ? (
                          <input
                            type="text"
                            value={subcategory.name}
                            onChange={(e) => handleSubcategoryNameChange(currentCategory.id, subcategory.id, e.target.value)}
                            onBlur={() => toggleSubcategoryEdit(currentCategory.id, subcategory.id)}
                            onKeyPress={(e) => e.key === 'Enter' && toggleSubcategoryEdit(currentCategory.id, subcategory.id)}
                            className="w-full text-lg font-semibold bg-transparent border-0 border-b-2 border-blue-500 focus:outline-none focus:border-blue-600 transition-colors duration-200"
                            autoFocus
                          />
                        ) : (
                          <div
                            onClick={() => toggleSubcategoryEdit(currentCategory.id, subcategory.id)}
                            className="text-lg font-semibold text-slate-900 cursor-pointer hover:text-blue-600 transition-colors duration-200 relative group-hover:text-blue-600"
                          >
                            {subcategory.name}
                            <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300 group-hover:w-full"></div>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="relative group">
                          <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-500 text-lg font-semibold transition-colors duration-200 group-focus-within:text-blue-600">$</span>
                          <input
                            type="number"
                            value={subcategory.budgetAmount || ''}
                            onChange={(e) => handleSubcategoryBudgetChange(currentCategory.id, subcategory.id, e.target.value)}
                            className="w-36 h-12 pl-8 pr-4 text-center text-lg font-semibold transition-all duration-200 focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 rounded-xl border-2 border-slate-200 bg-white"
                            placeholder="0"
                            min="0"
                          />
                        </div>

                        <button
                          onClick={() => removeSubcategory(currentCategory.id, subcategory.id)}
                          className="h-10 w-10 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 transition-all duration-200 hover:scale-110 opacity-0 group-hover:opacity-100 rounded-xl flex items-center justify-center"
                        >
                          <Icons.X className="w-4 h-4" strokeWidth={2} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Validation Messages */}
            {validationErrors.subcategories && (
              <ValidationMessage error={validationErrors.subcategories} className="mt-4" />
            )}
            {validationErrors.utilization && (
              <ValidationMessage warning={validationErrors.utilization} className="mt-4" />
            )}
          </div>

          {/* Progressive Navigation */}
          <div className="flex justify-between pt-8">
            <button
              onClick={goToPreviousCategory}
              className="group px-8 py-4 rounded-2xl text-lg font-semibold border-2 border-slate-300 hover:border-slate-400 hover:bg-slate-50 transition-all duration-300 flex items-center space-x-3"
            >
              <Icons.ArrowLeft className="w-5 h-5 group-hover:-translate-x-0.5 transition-transform duration-300" strokeWidth={2} />
              <span>{currentCategoryIndex === 0 ? 'Back to Strategy' : 'Previous Category'}</span>
            </button>

            <button
              onClick={() => {
                if (validateCurrentCategory()) {
                  goToNextCategory()
                }
              }}
              className="group px-8 py-4 rounded-2xl text-lg font-semibold bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center space-x-3"
            >
              <span>
                {currentCategoryIndex === totalCategories - 1 ? 'Review & Create Budget' : 'Next Category'}
              </span>
              <Icons.ArrowRight className="w-5 h-5 group-hover:translate-x-0.5 transition-transform duration-300" strokeWidth={2} />
            </button>
          </div>
        </div>
      )}

      {/* Empty State - No Categories */}
      {!currentCategory && totalCategories === 0 && (
        <div className="max-w-2xl mx-auto text-center py-16 space-y-6">
          <div className="w-24 h-24 bg-slate-100 rounded-3xl flex items-center justify-center mx-auto">
            <Icons.Category className="w-12 h-12 text-slate-400" strokeWidth={1.5} />
          </div>
          <div className="space-y-3">
            <h3 className="text-2xl font-bold text-slate-700">No Categories Available</h3>
            <p className="text-lg text-slate-500">Please select a budget template in the previous step to continue.</p>
          </div>
          <button
            onClick={onBack}
            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            Go Back to Strategy Selection
          </button>
        </div>
      )}
    </div>
  )
}


// Step 5: Professional Review & Create Budget
function ReviewCreateStep({ budgetData, onBack, onCreateBudget }) {
  const [isCreating, setIsCreating] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)

  // Calculate totals and allocations
  const totalMonthlyIncome = budgetData.totalMonthlyIncome || 0
  const totalAnnualIncome = budgetData.totalAnnualIncome || 0

  // Get category allocations from selected template
  const categoryAllocations = budgetData.selectedTemplate ? {
    needs: {
      amount: Math.round((totalMonthlyIncome * budgetData.selectedTemplate.breakdown.needs) / 100),
      percentage: budgetData.selectedTemplate.breakdown.needs,
      color: 'blue'
    },
    wants: {
      amount: Math.round((totalMonthlyIncome * budgetData.selectedTemplate.breakdown.wants) / 100),
      percentage: budgetData.selectedTemplate.breakdown.wants,
      color: 'orange'
    },
    savings: {
      amount: Math.round((totalMonthlyIncome * budgetData.selectedTemplate.breakdown.savings) / 100),
      percentage: budgetData.selectedTemplate.breakdown.savings,
      color: 'green'
    }
  } : {}

  const handleCreateBudget = async () => {
    setIsCreating(true)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    setIsCreating(false)
    setShowConfirmation(true)

    // Call the parent handler after a brief delay
    setTimeout(() => {
      onCreateBudget()
    }, 1500)
  }

  if (showConfirmation) {
    return (
      <div className="text-center space-y-12 py-16">
        <div className="space-y-8" style={{ animation: 'bounceIn 0.8s ease-out' }}>
          <div className="w-32 h-32 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full flex items-center justify-center mx-auto shadow-2xl shadow-emerald-500/30 animate-float">
            <Icons.CheckCircle className="w-16 h-16 text-white" strokeWidth={1.5} />
          </div>
          <div className="space-y-6">
            <h2 className="text-5xl lg:text-6xl font-bold text-gradient">Budget Created Successfully!</h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Your personalized budget <span className="font-bold text-emerald-600">"{budgetData.name}"</span> is ready to help you achieve your financial goals and build lasting wealth.
            </p>
          </div>
          <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-3xl p-10 border-2 border-emerald-200/60 max-w-2xl mx-auto shadow-xl">
            <div className="text-center space-y-4">
              <div className="text-6xl font-bold text-gradient">
                ${totalMonthlyIncome.toLocaleString()}
              </div>
              <div className="text-lg text-emerald-700 font-semibold">Monthly Budget Activated</div>
              <div className="flex items-center justify-center space-x-6 text-sm text-slate-600 mt-6">
                <div className="flex items-center space-x-2">
                  <Icons.Shield className="w-4 h-4 text-emerald-500" />
                  <span>Secure & Private</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icons.Rocket className="w-4 h-4 text-blue-500" />
                  <span>Ready to Use</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icons.Sparkles className="w-4 h-4 text-purple-500" />
                  <span>AI-Powered</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-12">
      {/* Professional Header */}
      <div className="text-center space-y-6" style={{ animation: 'fadeInScale 0.6s ease-out' }}>
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-600 to-emerald-700 rounded-3xl shadow-xl shadow-green-600/25 mb-6 animate-float">
          <Icons.CheckCircle className="w-10 h-10 text-white" strokeWidth={1.5} />
        </div>
        <div className="space-y-4">
          <h2 className="text-4xl lg:text-5xl font-bold text-gradient tracking-tight">
            Review & Finalize
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed font-medium">
            Review your comprehensive budget plan and activate your personalized financial strategy.
          </p>
        </div>

        {/* Professional Progress Indicator */}
        <div className="flex items-center justify-center space-x-3 text-sm text-slate-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="font-medium">Step 5 of 5</span>
          </div>
          <div className="w-1 h-1 bg-slate-300 rounded-full"></div>
          <span>Final Review</span>
        </div>
      </div>

      {/* Professional Budget Summary */}
      <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-10">

        {/* Professional Budget Overview */}
        <div className="space-y-8" style={{ animation: 'slideInUp 0.6s ease-out 0.2s both' }}>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center">
              <Icons.Document className="w-5 h-5 text-white" strokeWidth={2} />
            </div>
            <h3 className="text-2xl font-bold text-slate-900">Budget Overview</h3>
          </div>

          {/* Enhanced Basic Info Card */}
          <div className="bg-gradient-to-br from-white to-slate-50/50 rounded-3xl p-8 border-2 border-slate-200/60 shadow-lg">
            <div className="space-y-6">
              <div className="flex items-center justify-between py-3 border-b border-slate-200/60">
                <span className="text-lg font-semibold text-slate-600">Budget Name</span>
                <span className="text-lg font-bold text-slate-900">{budgetData.name}</span>
              </div>
              <div className="flex items-center justify-between py-3 border-b border-slate-200/60">
                <span className="text-lg font-semibold text-slate-600">Period</span>
                <span className="text-lg font-bold text-slate-900">
                  {new Date(budgetData.startDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {new Date(budgetData.endDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}
                </span>
              </div>
              <div className="flex items-center justify-between py-3">
                <span className="text-lg font-semibold text-slate-600">Strategy</span>
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                    <Icons.Template className="w-3 h-3 text-white" strokeWidth={2} />
                  </div>
                  <span className="text-lg font-bold text-slate-900">
                    {budgetData.selectedTemplate?.name}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Professional Income Summary */}
          <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-3xl p-8 border-2 border-emerald-200/60 shadow-lg">
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center">
                  <Icons.CurrencyDollar className="w-5 h-5 text-white" strokeWidth={2} />
                </div>
                <h4 className="text-xl font-bold text-emerald-900">Income Summary</h4>
              </div>
              <div className="space-y-4">
                {budgetData.incomeStreams?.map((stream) => {
                  const IconComponent = Icons[stream.category === 'employment' ? 'Briefcase' :
                                                stream.category === 'business' ? 'Code' :
                                                stream.category === 'investment' ? 'TrendingUp' : 'CurrencyDollar']
                  return (
                    <div key={stream.id} className="flex items-center justify-between p-4 bg-white rounded-2xl border border-emerald-200/60">
                      <div className="flex items-center space-x-4">
                        <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                          <IconComponent className="w-4 h-4 text-white" strokeWidth={2} />
                        </div>
                        <span className="text-base font-semibold text-emerald-800">{stream.name}</span>
                      </div>
                      <span className="text-base font-bold text-emerald-900">
                        ${Math.round(stream.monthlyAmount).toLocaleString()}/mo
                      </span>
                    </div>
                  )
                })}
                <div className="border-t border-green-300 pt-3 mt-3">
                  <div className="flex items-center justify-between">
                    <span className="text-body-md font-bold text-green-900">Total Monthly Income</span>
                    <span className="text-heading-lg font-bold text-green-900">
                      ${totalMonthlyIncome.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Budget Allocation */}
        <div className="space-y-6">
          <h3 className="text-heading-lg font-bold text-gray-900">Budget Allocation</h3>

          {/* Category Breakdown */}
          <div className="space-y-4">
            {Object.entries(categoryAllocations).map(([key, allocation]) => (
              <div key={key} className={`bg-${allocation.color}-50 rounded-2xl p-6 border border-${allocation.color}-200`}>
                <div className="flex items-center justify-between mb-4">
                  <h4 className={`text-heading-md font-bold text-${allocation.color}-700`}>
                    {key === 'needs' ? 'NEEDS (Essential)' :
                     key === 'wants' ? 'WANTS (Lifestyle)' :
                     'SMILE (Future)'}
                  </h4>
                  <div className="text-right">
                    <div className={`text-display-lg font-bold text-${allocation.color}-600`}>
                      ${allocation.amount.toLocaleString()}
                    </div>
                    <div className={`text-label-sm text-${allocation.color}-700`}>
                      {allocation.percentage}% of income
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`bg-gradient-to-r from-${allocation.color}-400 to-${allocation.color}-600 h-3 rounded-full transition-all duration-700 ease-out`}
                    style={{ width: `${allocation.percentage}%` }}
                  ></div>
                </div>

                <div className="mt-3 text-center">
                  <span className={`text-label-sm text-${allocation.color}-700`}>
                    {key === 'needs' ? 'Rent, groceries, utilities, transportation' :
                     key === 'wants' ? 'Entertainment, dining, hobbies, shopping' :
                     'Emergency fund, retirement, investments, debt payoff'}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Total Verification */}
          <div className="bg-blue-50 rounded-2xl p-6 border border-blue-200">
            <div className="text-center space-y-3">
              <h4 className="text-heading-md font-bold text-blue-900">Budget Verification</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-display-lg font-bold text-blue-600">
                    ${Object.values(categoryAllocations).reduce((sum, cat) => sum + cat.amount, 0).toLocaleString()}
                  </div>
                  <div className="text-label-sm text-blue-700">Total Allocated</div>
                </div>
                <div>
                  <div className="text-display-lg font-bold text-blue-600">
                    ${totalMonthlyIncome.toLocaleString()}
                  </div>
                  <div className="text-label-sm text-blue-700">Monthly Income</div>
                </div>
              </div>
              <div className="flex items-center justify-center space-x-2 text-green-600">
                <span className="text-xl">✓</span>
                <span className="text-body-sm font-medium">Budget is balanced and ready!</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Professional Action Buttons */}
      <div className="flex justify-between pt-12 border-t-2 border-slate-200/60" style={{ animation: 'slideInUp 0.6s ease-out 0.8s both' }}>
        <Button
          variant="outline"
          onClick={onBack}
          className="group px-8 py-5 rounded-2xl text-lg font-semibold border-2 border-slate-300 hover:border-slate-400 hover:bg-slate-50 transition-all duration-300"
          disabled={isCreating}
        >
          <div className="flex items-center space-x-3">
            <Icons.ArrowLeft className="w-5 h-5 group-hover:-translate-x-0.5 transition-transform duration-300" strokeWidth={2} />
            <span>Back to Categories</span>
          </div>
        </Button>

        <Button
          onClick={handleCreateBudget}
          disabled={isCreating}
          className="group relative bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-12 py-5 rounded-2xl text-lg font-bold shadow-xl shadow-green-600/25 hover:shadow-2xl hover:shadow-green-600/30 transition-all duration-300 hover:scale-105 overflow-hidden disabled:opacity-75"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          {isCreating && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
          )}
          <div className="relative flex items-center space-x-4">
            {isCreating ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Creating Your Budget...</span>
              </>
            ) : (
              <>
                <Icons.Rocket className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" strokeWidth={2} />
                <span>Create My Budget</span>
              </>
            )}
          </div>
        </Button>
      </div>
    </div>
  )
}

// Income Stream Modal Component
function IncomeStreamModal({ stream, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    name: stream?.name || '',
    amount: stream?.amount || '',
    frequency: stream?.frequency || 'monthly',
    category: stream?.category || 'employment',
    isActive: stream?.isActive ?? true,
    notes: stream?.notes || ''
  })

  const [errors, setErrors] = useState({})

  const categories = [
    {
      value: 'employment',
      label: 'Employment',
      icon: Icons.Briefcase,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-700'
    },
    {
      value: 'business',
      label: 'Business',
      icon: Icons.Building,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      textColor: 'text-purple-700'
    },
    {
      value: 'investment',
      label: 'Investment',
      icon: Icons.TrendingUp,
      color: 'from-emerald-500 to-emerald-600',
      bgColor: 'bg-emerald-50',
      borderColor: 'border-emerald-200',
      textColor: 'text-emerald-700'
    },
    {
      value: 'government',
      label: 'Government',
      icon: Icons.Shield,
      color: 'from-indigo-500 to-indigo-600',
      bgColor: 'bg-indigo-50',
      borderColor: 'border-indigo-200',
      textColor: 'text-indigo-700'
    },
    {
      value: 'other',
      label: 'Other',
      icon: Icons.DollarSign,
      color: 'from-amber-500 to-amber-600',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200',
      textColor: 'text-amber-700'
    }
  ]

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Stream name is required'
    }

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Amount must be greater than 0'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (!validateForm()) return

    const amount = parseFloat(formData.amount)
    let monthlyAmount, annualAmount

    if (formData.frequency === 'monthly') {
      monthlyAmount = amount
      annualAmount = amount * 12
    } else {
      annualAmount = amount
      monthlyAmount = amount / 12
    }

    const streamData = {
      ...formData,
      amount,
      monthlyAmount,
      annualAmount
    }

    onSave(streamData)
  }

  const previewMonthly = formData.frequency === 'monthly'
    ? parseFloat(formData.amount) || 0
    : (parseFloat(formData.amount) || 0) / 12

  const previewAnnual = formData.frequency === 'annually'
    ? parseFloat(formData.amount) || 0
    : (parseFloat(formData.amount) || 0) * 12

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center p-4 z-50 animate-in fade-in duration-300">
      <div
        className="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto animate-in slide-in-from-bottom-4 duration-300"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
      >
        <div className="p-8">
          {/* Professional Modal Header */}
          <div className="flex items-start justify-between mb-8">
            <div className="flex items-center space-x-4">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                <Icons.DollarSign className="w-7 h-7 text-white" strokeWidth={2.5} />
              </div>
              <div className="space-y-1">
                <h2 className="text-2xl font-bold text-slate-900">
                  {stream ? 'Edit Income Stream' : 'Add Income Stream'}
                </h2>
                <p className="text-base text-slate-600">
                  {stream ? 'Update your income source details' : 'Add a new source of income to your budget'}
                </p>
              </div>
            </div>
            <button
              onClick={onCancel}
              className="w-10 h-10 rounded-xl hover:bg-slate-100 flex items-center justify-center transition-colors duration-200 group"
            >
              <Icons.X className="w-5 h-5 text-slate-400 group-hover:text-slate-600" strokeWidth={2} />
            </button>
          </div>

          {/* Professional Form */}
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Stream Name - Enhanced */}
            <div className="space-y-3">
              <label className="text-lg font-semibold text-slate-900 flex items-center space-x-2">
                <Icons.Type className="w-4 h-4 text-slate-500" strokeWidth={2} />
                <span>Stream Name *</span>
              </label>
              <div className="relative group">
                <Input
                  placeholder="e.g., Primary Salary, Freelance Work"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className={`h-14 text-lg font-semibold px-4 border-2 rounded-xl transition-all duration-300 bg-white shadow-sm hover:shadow-md placeholder:text-slate-400 placeholder:font-normal ${
                    errors.name
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                      : 'border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 group-hover:border-slate-300'
                  }`}
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
              {errors.name && (
                <div className="flex items-center space-x-2 text-red-600">
                  <Icons.AlertCircle className="w-4 h-4" strokeWidth={2} />
                  <p className="text-sm font-medium">{errors.name}</p>
                </div>
              )}
            </div>

            {/* Amount and Frequency - Enhanced */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-3">
                <label className="text-lg font-semibold text-slate-900 flex items-center space-x-2">
                  <Icons.DollarSign className="w-4 h-4 text-slate-500" strokeWidth={2} />
                  <span>Amount *</span>
                </label>
                <div className="relative group">
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-500 text-lg font-semibold">$</div>
                  <Input
                    type="number"
                    placeholder="0.00"
                    value={formData.amount}
                    onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                    className={`h-14 text-lg font-semibold pl-10 pr-4 border-2 rounded-xl transition-all duration-300 bg-white shadow-sm hover:shadow-md ${
                      errors.amount
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                        : 'border-slate-200 focus:border-emerald-500 focus:ring-emerald-500/20 group-hover:border-slate-300'
                    }`}
                    step="0.01"
                    min="0"
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-emerald-500/5 to-green-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
                {errors.amount && (
                  <div className="flex items-center space-x-2 text-red-600">
                    <Icons.AlertCircle className="w-4 h-4" strokeWidth={2} />
                    <p className="text-sm font-medium">{errors.amount}</p>
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <label className="text-lg font-semibold text-slate-900 flex items-center space-x-2">
                  <Icons.Calendar className="w-4 h-4 text-slate-500" strokeWidth={2} />
                  <span>Frequency *</span>
                </label>
                <div className="bg-slate-100 rounded-xl p-1.5 h-14">
                  <div className="flex h-full">
                    <button
                      type="button"
                      onClick={() => setFormData({ ...formData, frequency: 'monthly' })}
                      className={`flex-1 rounded-lg text-base font-semibold transition-all duration-300 flex items-center justify-center ${
                        formData.frequency === 'monthly'
                          ? 'bg-white text-blue-600 shadow-md shadow-blue-600/10'
                          : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                      }`}
                    >
                      Monthly
                    </button>
                    <button
                      type="button"
                      onClick={() => setFormData({ ...formData, frequency: 'annually' })}
                      className={`flex-1 rounded-lg text-base font-semibold transition-all duration-300 flex items-center justify-center ${
                        formData.frequency === 'annually'
                          ? 'bg-white text-blue-600 shadow-md shadow-blue-600/10'
                          : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                      }`}
                    >
                      Annually
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Professional Category Selection */}
            <div className="space-y-4">
              <label className="text-lg font-semibold text-slate-900 flex items-center space-x-2">
                <Icons.Tag className="w-4 h-4 text-slate-500" strokeWidth={2} />
                <span>Category</span>
              </label>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {categories.map((category) => {
                  const IconComponent = category.icon
                  const isSelected = formData.category === category.value

                  return (
                    <button
                      key={category.value}
                      type="button"
                      onClick={() => setFormData({ ...formData, category: category.value })}
                      className={`group relative p-4 rounded-2xl border-2 transition-all duration-300 hover:shadow-lg ${
                        isSelected
                          ? `${category.borderColor} ${category.bgColor} shadow-md`
                          : 'border-slate-200 hover:border-slate-300 bg-white hover:bg-slate-50'
                      }`}
                    >
                      <div className="flex flex-col items-center space-y-3">
                        <div className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ${
                          isSelected
                            ? `bg-gradient-to-br ${category.color} shadow-lg shadow-${category.color.split('-')[1]}-500/25`
                            : 'bg-slate-100 group-hover:bg-slate-200'
                        }`}>
                          <IconComponent
                            className={`w-5 h-5 ${isSelected ? 'text-white' : 'text-slate-600'}`}
                            strokeWidth={2}
                          />
                        </div>
                        <div className={`text-sm font-semibold transition-colors duration-300 ${
                          isSelected ? category.textColor : 'text-slate-700 group-hover:text-slate-900'
                        }`}>
                          {category.label}
                        </div>
                      </div>

                      {/* Selection indicator */}
                      {isSelected && (
                        <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                          <Icons.Check className="w-3 h-3 text-white" strokeWidth={3} />
                        </div>
                      )}
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Enhanced Notes Section */}
            <div className="space-y-3">
              <label className="text-lg font-semibold text-slate-900 flex items-center space-x-2">
                <Icons.FileText className="w-4 h-4 text-slate-500" strokeWidth={2} />
                <span>Notes</span>
                <span className="text-sm font-normal text-slate-500">(Optional)</span>
              </label>
              <div className="relative group">
                <textarea
                  placeholder="Add any additional details about this income stream..."
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  className="w-full h-24 px-4 py-3 border-2 border-slate-200 rounded-xl resize-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 bg-white shadow-sm hover:shadow-md group-hover:border-slate-300 placeholder:text-slate-400"
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            {/* Professional Preview */}
            {formData.amount && (
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50/50 rounded-2xl p-6 border border-blue-200/60 shadow-sm">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <Icons.Eye className="w-4 h-4 text-white" strokeWidth={2} />
                  </div>
                  <h4 className="text-lg font-bold text-blue-900">Preview</h4>
                </div>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center space-y-1">
                    <div className="text-2xl font-bold text-blue-900">
                      ${Math.round(previewMonthly).toLocaleString()}
                    </div>
                    <div className="text-sm font-medium text-blue-700">Monthly Income</div>
                  </div>
                  <div className="text-center space-y-1">
                    <div className="text-2xl font-bold text-blue-900">
                      ${Math.round(previewAnnual).toLocaleString()}
                    </div>
                    <div className="text-sm font-medium text-blue-700">Annual Income</div>
                  </div>
                </div>
              </div>
            )}

            {/* Professional Form Actions */}
            <div className="flex justify-end space-x-4 pt-8 border-t border-slate-200">
              <button
                type="button"
                onClick={onCancel}
                className="px-8 py-3 text-base font-semibold text-slate-700 bg-white border-2 border-slate-200 rounded-xl hover:bg-slate-50 hover:border-slate-300 transition-all duration-300 shadow-sm hover:shadow-md"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-8 py-3 text-base font-semibold text-white bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg shadow-blue-600/25 hover:shadow-xl hover:shadow-blue-600/30 flex items-center space-x-2"
              >
                <Icons.Plus className="w-4 h-4" strokeWidth={2} />
                <span>{stream ? 'Update Stream' : 'Add Stream'}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
