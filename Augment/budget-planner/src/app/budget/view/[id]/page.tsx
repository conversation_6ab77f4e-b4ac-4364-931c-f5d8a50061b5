'use client'

import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { AppLayout } from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import * as Icons from 'lucide-react'

interface BudgetViewPageProps {}

export default function BudgetViewPage({}: BudgetViewPageProps) {
  const params = useParams()
  const router = useRouter()
  const [budgetData, setBudgetData] = useState(null)
  const [loading, setLoading] = useState(true)

  // Helper functions for category mapping
  const getDescriptionForCategory = (categoryName, isCustom = false) => {
    if (isCustom) {
      // For custom categories, try to use stored description or generate a generic one
      return `Custom budget category: ${categoryName}`
    }

    switch (categoryName?.toLowerCase()) {
      case 'needs': return 'Essential expenses you cannot avoid'
      case 'wants': return 'Things you enjoy but could live without'
      case 'smile': return 'Building your financial future and security'
      case 'savings': return 'Emergency fund, retirement, investments'
      default: return 'Budget category'
    }
  }

  const getColorForCategory = (categoryName, isCustom = false, index = 0) => {
    if (isCustom) {
      // For custom categories, use a rotating color palette
      const customColors = ['blue', 'purple', 'green', 'orange', 'teal', 'pink', 'indigo', 'red']
      return customColors[index % customColors.length]
    }

    switch (categoryName?.toLowerCase()) {
      case 'needs': return 'blue'
      case 'wants': return 'orange'
      case 'smile': return 'green'
      case 'savings': return 'green'
      default: return 'gray'
    }
  }

  const getCategoryIcon = (category, isCustom = false) => {
    const iconClass = `w-6 h-6 text-${category.color}-600`

    if (isCustom) {
      // For custom categories, use a rotating icon set
      const customIcons = [
        <Icons.Target className={iconClass} strokeWidth={2} />,
        <Icons.PieChart className={iconClass} strokeWidth={2} />,
        <Icons.TrendingUp className={iconClass} strokeWidth={2} />,
        <Icons.DollarSign className={iconClass} strokeWidth={2} />,
        <Icons.Wallet className={iconClass} strokeWidth={2} />,
        <Icons.CreditCard className={iconClass} strokeWidth={2} />,
        <Icons.Banknote className={iconClass} strokeWidth={2} />,
        <Icons.Calculator className={iconClass} strokeWidth={2} />
      ]

      // Use category name hash to consistently assign icons
      const iconIndex = category.name.length % customIcons.length
      return customIcons[iconIndex]
    }

    // Standard category icons
    switch (category.id?.toLowerCase()) {
      case 'needs': return <Icons.Home className={iconClass} strokeWidth={2} />
      case 'wants': return <Icons.ShoppingCart className={iconClass} strokeWidth={2} />
      case 'smile': return <Icons.TrendingUp className={iconClass} strokeWidth={2} />
      case 'savings': return <Icons.PiggyBank className={iconClass} strokeWidth={2} />
      default: return <Icons.Circle className={iconClass} strokeWidth={2} />
    }
  }

  // Load real budget data from database
  useEffect(() => {
    const loadBudgetData = async () => {
      try {
        console.log('🔄 Loading budget details for ID:', params.id)
        setLoading(true)

        // Import and use the API service
        const { budgetApi } = await import('@/lib/api/budgets')
        const budget = await budgetApi.getBudget(params.id)

        console.log('✅ Found budget:', budget.name)
        console.log('📊 Budget categories:', budget.categories)

        setBudgetData(budget)
        console.log('🎉 Budget data loaded successfully!')
        console.log('📋 Transformed categories:', budget.categories)
        console.log('🔢 Total categories:', budget.categories?.length)

      } catch (error) {
        console.error('❌ Error loading budget data:', error)

        // Fallback to localStorage if API fails (for development)
        try {
          console.log('🔄 Falling back to localStorage...')
          const storedBudgets = localStorage.getItem('budgets')
          if (!storedBudgets) {
            console.log('❌ No budgets found in localStorage')
            setBudgetData(null)
            setLoading(false)
            return
          }

          const budgetList = JSON.parse(storedBudgets)
          console.log('📊 Found stored budgets:', budgetList.length)

          // Find the specific budget by ID
          const budget = budgetList.find(b => b.id === params.id)

          if (!budget) {
            console.log('❌ Budget not found with ID:', params.id)
            setBudgetData(null)
            setLoading(false)
            return
          }

          console.log('✅ Found budget:', budget.name)
          console.log('📊 Budget categories:', budget.budgetCategories)
          console.log('🔧 Budget strategy:', budget.strategy)
          console.log('🎯 Selected template:', budget.selectedTemplate)

          // Check if this is a custom budget with custom categories
          const isCustomBudget = budget.strategy === 'custom' || budget.selectedTemplate?.isCustom
          console.log('🎨 Is custom budget:', isCustomBudget)

          // Transform budget data to match the expected format
          const transformedBudget = {
            id: budget.id,
            name: budget.name,
            description: budget.description,
            startDate: budget.startDate,
            endDate: budget.endDate,
            totalMonthlyIncome: budget.totalIncome,
            totalAllocated: budget.budgetCategories?.reduce((sum, cat) => sum + cat.amount, 0) || 0,
            remaining: budget.totalIncome - (budget.budgetCategories?.reduce((sum, cat) => sum + cat.amount, 0) || 0),
            status: budget.status || 'active',
            createdAt: budget.createdAt,
            incomeStreams: budget.incomeStreams || [],
            isCustom: isCustomBudget,
            categories: budget.budgetCategories?.map((cat, index) => ({
              id: cat.categoryId || cat.id,
              name: cat.categoryName,
              description: cat.description || getDescriptionForCategory(cat.categoryName, isCustomBudget),
              color: cat.color || getColorForCategory(cat.categoryName, isCustomBudget, index),
              percentage: cat.percentage,
              allocated: cat.amount,
              spent: cat.spent || 0,
              remaining: cat.remaining || (cat.amount - (cat.spent || 0)),
              subcategories: [] // Will be populated with actual subcategories if available
            })) || []
          }

          setBudgetData(transformedBudget)
          console.log('✅ Loaded from localStorage fallback')
        } catch (fallbackError) {
          console.error('❌ Fallback also failed:', fallbackError)
          setBudgetData(null)
        }
      } finally {
        setLoading(false)
      }
    }



    loadBudgetData()
  }, [params.id])

  if (loading) {
    return (
      <AppLayout>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-3xl flex items-center justify-center mx-auto animate-pulse">
              <Icons.PieChart className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-slate-900">Loading Budget...</h2>
            <p className="text-slate-600">Preparing your financial overview</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  if (!budgetData) {
    return (
      <AppLayout>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-rose-600 rounded-3xl flex items-center justify-center mx-auto">
              <Icons.AlertCircle className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-slate-900">Budget Not Found</h2>
            <p className="text-slate-600">The budget you're looking for doesn't exist</p>
            <Button onClick={() => router.push('/budget')} className="mt-4">
              Back to Budgets
            </Button>
          </div>
        </div>
      </AppLayout>
    )
  }

  const totalSpent = budgetData.categories?.reduce((sum, cat) => sum + (cat.spent || 0), 0) || 0
  const totalRemaining = budgetData.categories?.reduce((sum, cat) => sum + (cat.remaining || 0), 0) || 0
  const spendingProgress = budgetData.totalAllocated > 0 ? (totalSpent / budgetData.totalAllocated) * 100 : 0

  return (
    <AppLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 -m-6">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(59,130,246,0.15)_1px,transparent_0)] bg-[length:24px_24px] opacity-30 pointer-events-none"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">
          
          {/* Header Section */}
          <div className="space-y-8 mb-12">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-slate-500">
              <button 
                onClick={() => router.push('/budget')}
                className="hover:text-slate-700 transition-colors"
              >
                Budget Management
              </button>
              <Icons.ChevronRight className="w-4 h-4" />
              <span className="text-slate-900 font-semibold">{budgetData.name}</span>
            </nav>

            {/* Main Header */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-6 lg:space-y-0">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-3xl flex items-center justify-center shadow-xl shadow-blue-600/25">
                    <Icons.PieChart className="w-8 h-8 text-white" strokeWidth={1.5} />
                  </div>
                  <div>
                    <h1 className="text-4xl lg:text-5xl font-bold text-gradient tracking-tight">
                      {budgetData.name}
                    </h1>
                    <p className="text-lg text-slate-600 font-medium">
                      {new Date(budgetData.startDate).toLocaleDateString('en-US', { 
                        month: 'long', 
                        day: 'numeric' 
                      })} - {new Date(budgetData.endDate).toLocaleDateString('en-US', { 
                        month: 'long', 
                        day: 'numeric', 
                        year: 'numeric' 
                      })}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Badge 
                    variant="outline" 
                    className="bg-green-50 text-green-700 border-green-200 font-semibold px-3 py-1"
                  >
                    <Icons.CheckCircle className="w-4 h-4 mr-2" />
                    Active Budget
                  </Badge>
                  <span className="text-sm text-slate-500">
                    Created {new Date(budgetData.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center space-x-3">
                <Button variant="outline" className="flex items-center space-x-2">
                  <Icons.Edit className="w-4 h-4" />
                  <span>Edit Budget</span>
                </Button>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Icons.Copy className="w-4 h-4" />
                  <span>Duplicate</span>
                </Button>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Icons.Share className="w-4 h-4" />
                  <span>Share</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Budget Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {/* Total Income */}
            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold text-blue-700 flex items-center space-x-2">
                  <Icons.DollarSign className="w-5 h-5" />
                  <span>Monthly Income</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-900 mb-2">
                  ${budgetData.totalMonthlyIncome.toLocaleString()}
                </div>
                <div className="text-sm text-blue-600 font-medium">Total available</div>
              </CardContent>
            </Card>

            {/* Total Allocated */}
            <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold text-purple-700 flex items-center space-x-2">
                  <Icons.Target className="w-5 h-5" />
                  <span>Allocated</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-900 mb-2">
                  ${budgetData.totalAllocated.toLocaleString()}
                </div>
                <div className="text-sm text-purple-600 font-medium">
                  {budgetData.totalMonthlyIncome > 0 ? Math.round((budgetData.totalAllocated / budgetData.totalMonthlyIncome) * 100) : 0}% of income
                </div>
              </CardContent>
            </Card>

            {/* Total Spent */}
            <Card className="bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold text-orange-700 flex items-center space-x-2">
                  <Icons.CreditCard className="w-5 h-5" />
                  <span>Spent</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-orange-900 mb-2">
                  ${totalSpent.toLocaleString()}
                </div>
                <div className="text-sm text-orange-600 font-medium">
                  {budgetData.totalAllocated > 0 ? Math.round((totalSpent / budgetData.totalAllocated) * 100) : 0}% of budget
                </div>
              </CardContent>
            </Card>

            {/* Remaining */}
            <Card className={`bg-gradient-to-br border-2 shadow-lg hover:shadow-xl transition-all duration-300 ${
              totalRemaining >= 0
                ? 'from-green-50 to-emerald-50 border-green-200'
                : 'from-red-50 to-rose-50 border-red-200'
            }`}>
              <CardHeader className="pb-3">
                <CardTitle className={`text-lg font-semibold flex items-center space-x-2 ${
                  totalRemaining >= 0 ? 'text-green-700' : 'text-red-700'
                }`}>
                  {totalRemaining >= 0 ? <Icons.Plus className="w-5 h-5" /> : <Icons.Minus className="w-5 h-5" />}
                  <span>Remaining</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-3xl font-bold mb-2 ${
                  totalRemaining >= 0 ? 'text-green-900' : 'text-red-900'
                }`}>
                  ${Math.abs(totalRemaining).toLocaleString()}
                </div>
                <div className={`text-sm font-medium ${
                  totalRemaining >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {totalRemaining >= 0 ? 'Available to spend' : 'Over budget'}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Spending Progress */}
          <Card className="mb-12 bg-white/80 backdrop-blur-sm border-2 border-slate-200 shadow-xl">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-slate-900 flex items-center space-x-3">
                <Icons.TrendingUp className="w-6 h-6 text-blue-600" />
                <span>Spending Progress</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-slate-700">
                    ${totalSpent.toLocaleString()} of ${budgetData.totalAllocated.toLocaleString()}
                  </span>
                  <span className="text-lg font-bold text-blue-600">
                    {Math.round(spendingProgress)}%
                  </span>
                </div>
                <Progress value={spendingProgress} className="h-4" />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-blue-600">${totalSpent.toLocaleString()}</div>
                  <div className="text-sm text-slate-600">Total Spent</div>
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-green-600">${totalRemaining.toLocaleString()}</div>
                  <div className="text-sm text-slate-600">Remaining</div>
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round(((new Date().getDate()) / new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate()) * 100)}%
                  </div>
                  <div className="text-sm text-slate-600">Month Progress</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Category Breakdown */}
          <div className="space-y-8">
            <h2 className="text-3xl font-bold text-slate-900 text-center">Category Breakdown</h2>

            {/* Debug info */}
            {(!budgetData.categories || budgetData.categories.length === 0) && (
              <div className="text-center p-8 bg-yellow-50 border border-yellow-200 rounded-xl">
                <p className="text-yellow-800 font-medium">No categories found for this budget.</p>
                <p className="text-yellow-600 text-sm mt-2">
                  Categories count: {budgetData.categories?.length || 0} |
                  Is Custom: {budgetData.isCustom ? 'Yes' : 'No'}
                </p>
              </div>
            )}

            <div className={`grid grid-cols-1 gap-8 ${
              budgetData.categories?.length <= 2 ? 'lg:grid-cols-2' :
              budgetData.categories?.length === 3 ? 'lg:grid-cols-3' :
              budgetData.categories?.length === 4 ? 'lg:grid-cols-2 xl:grid-cols-4' :
              'lg:grid-cols-2 xl:grid-cols-3'
            }`}>
              {budgetData.categories?.map((category, index) => {
                const spentPercentage = category.allocated > 0 ? (category.spent / category.allocated) * 100 : 0
                const isOverBudget = category.spent > category.allocated

                return (
                  <Card
                    key={category.id}
                    className="bg-white border-2 border-slate-200 hover:border-purple-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                    style={{
                      animation: `slideInUp 0.6s ease-out ${index * 0.1}s both`
                    }}
                  >
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-12 h-12 bg-${category.color}-100 rounded-xl flex items-center justify-center`}>
                            {getCategoryIcon(category, budgetData.isCustom)}
                          </div>
                          <div>
                            <div className="text-lg font-bold text-slate-900">{category.name}</div>
                            <div className="text-sm text-slate-500">{category.description}</div>
                          </div>
                        </div>
                        <Badge variant="outline" className={`bg-${category.color}-50 text-${category.color}-700 border-${category.color}-200 font-bold`}>
                          {category.percentage}%
                        </Badge>
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="space-y-6">
                      {/* Spending Overview */}
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-semibold text-slate-700">Spending Progress</span>
                          <span className={`text-sm font-bold ${isOverBudget ? 'text-red-600' : 'text-slate-700'}`}>
                            ${category.spent.toLocaleString()} / ${category.allocated.toLocaleString()}
                          </span>
                        </div>

                        <Progress
                          value={Math.min(spentPercentage, 100)}
                          className={`h-3 ${isOverBudget ? 'bg-red-100' : ''}`}
                        />

                        <div className="flex justify-between text-sm">
                          <span className={`font-medium ${isOverBudget ? 'text-red-600' : 'text-green-600'}`}>
                            {isOverBudget ? 'Over budget' : 'On track'}
                          </span>
                          <span className="text-slate-500">
                            {Math.round(spentPercentage)}% used
                          </span>
                        </div>
                      </div>

                      {/* Budget Breakdown */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-slate-50 rounded-xl">
                          <div className={`text-xl font-bold text-${category.color}-600`}>
                            ${category.allocated.toLocaleString()}
                          </div>
                          <div className="text-xs text-slate-500 font-medium">Allocated</div>
                        </div>
                        <div className="text-center p-3 bg-slate-50 rounded-xl">
                          <div className={`text-xl font-bold ${category.remaining >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            ${Math.abs(category.remaining).toLocaleString()}
                          </div>
                          <div className="text-xs text-slate-500 font-medium">
                            {category.remaining >= 0 ? 'Remaining' : 'Over'}
                          </div>
                        </div>
                      </div>

                      {/* Subcategories */}
                      {category.subcategories && category.subcategories.length > 0 && (
                        <div className="space-y-3">
                          <div className="text-sm font-semibold text-slate-700 border-b border-slate-200 pb-2">
                            Subcategories
                          </div>
                          <div className="space-y-2">
                            {category.subcategories.map((sub, idx) => (
                              <div key={idx} className="flex justify-between items-center text-sm">
                                <span className="text-slate-600">{sub.name}</span>
                                <div className="text-right">
                                  <div className="font-medium text-slate-700">
                                    ${sub.spent?.toLocaleString() || '0'} / ${sub.allocated?.toLocaleString()}
                                  </div>
                                  <div className={`text-xs ${
                                    (sub.spent || 0) <= sub.allocated ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {sub.allocated - (sub.spent || 0) >= 0 ?
                                      `$${(sub.allocated - (sub.spent || 0)).toLocaleString()} left` :
                                      `$${((sub.spent || 0) - sub.allocated).toLocaleString()} over`
                                    }
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Quick Actions */}
                      <div className="flex space-x-2 pt-2">
                        <Button variant="outline" size="sm" className="flex-1 text-xs">
                          <Icons.Plus className="w-3 h-3 mr-1" />
                          Add Expense
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1 text-xs">
                          <Icons.Edit className="w-3 h-3 mr-1" />
                          Edit Category
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

        </div>
      </div>
    </AppLayout>
  )
}
