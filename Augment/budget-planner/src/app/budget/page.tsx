'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { AppLayout } from '@/components/layout/AppLayout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import * as Icons from 'lucide-react'

interface Budget {
  id: string
  name: string
  startDate: string
  endDate: string
  totalIncome: number
  totalAllocated: number
  totalSpent: number
  status: 'active' | 'completed' | 'draft'
  createdAt: string
  categories: number
}

// Mock data for LF design
const currentBudget = {
  id: '1',
  name: 'October 2024 Budget',
  template: '50/30/20 Rule',
  totalIncome: 5500.00,
  totalBudgeted: 5500.00,
  totalSpent: 3247.85,
  remaining: 2252.15,
  status: 'On Track',
  period: 'October 1 - October 31, 2024'
}

const budgetCategories = [
  {
    id: '1',
    name: 'NEED',
    budgeted: 2750.00, // 50%
    spent: 2100.45,
    remaining: 649.55,
    percentage: 76,
    color: 'blue',
    subcategories: [
      { name: 'Rent/Mortgage', budgeted: 1500.00, spent: 1500.00, remaining: 0 },
      { name: 'Groceries', budgeted: 600.00, spent: 450.45, remaining: 149.55 },
      { name: 'Transportation', budgeted: 400.00, spent: 150.00, remaining: 250.00 },
      { name: 'Utilities', budgeted: 250.00, spent: 0, remaining: 250.00 }
    ]
  },
  {
    id: '2',
    name: 'WANTS',
    budgeted: 1650.00, // 30%
    spent: 847.40,
    remaining: 802.60,
    percentage: 51,
    color: 'orange',
    subcategories: [
      { name: 'Entertainment', budgeted: 400.00, spent: 247.40, remaining: 152.60 },
      { name: 'Dining Out', budgeted: 300.00, spent: 180.00, remaining: 120.00 },
      { name: 'Shopping', budgeted: 500.00, spent: 420.00, remaining: 80.00 },
      { name: 'Hobbies', budgeted: 450.00, spent: 0, remaining: 450.00 }
    ]
  },
  {
    id: '3',
    name: 'SMILE',
    budgeted: 1100.00, // 20%
    spent: 300.00,
    remaining: 800.00,
    percentage: 27,
    color: 'green',
    subcategories: [
      { name: 'Emergency Fund', budgeted: 550.00, spent: 0, remaining: 550.00 },
      { name: 'Investments', budgeted: 400.00, spent: 300.00, remaining: 100.00 },
      { name: 'Debt Payment', budgeted: 150.00, spent: 0, remaining: 150.00 }
    ]
  }
]

const budgetTemplates = [
  {
    id: '1',
    name: '50/30/20 Rule',
    description: 'Balanced approach: 50% needs, 30% wants, 20% savings',
    popular: true,
    breakdown: { needs: 50, wants: 30, savings: 20 }
  },
  {
    id: '2',
    name: 'Zero-Based Budget',
    description: 'Every dollar has a purpose - income minus expenses equals zero',
    popular: false,
    breakdown: { needs: 60, wants: 25, savings: 15 }
  },
  {
    id: '3',
    name: 'Conservative',
    description: 'Focus on savings: 60% needs, 20% wants, 20% savings',
    popular: false,
    breakdown: { needs: 60, wants: 20, savings: 20 }
  }
]

const budgetHistory = [
  { month: 'September 2024', template: '50/30/20 Rule', status: 'Completed', variance: '+$125.50' },
  { month: 'August 2024', template: '50/30/20 Rule', status: 'Overspent', variance: '-$89.25' },
  { month: 'July 2024', template: 'Zero-Based', status: 'Completed', variance: '+$0.00' }
]

export default function BudgetPage() {
  const router = useRouter()
  const [budgets, setBudgets] = useState<Budget[]>([])
  const [loading, setLoading] = useState(true)

  // CLIENT-SIDE DATA MANAGEMENT - USING DATABASE API
  useEffect(() => {
    const loadBudgetData = async () => {
      try {
        console.log('🔄 Loading budget data from database...')
        setLoading(true)

        // Import and use the API service
        const { budgetApi } = await import('@/lib/api/budgets')
        const budgetList = await budgetApi.getBudgets()

        console.log('📊 Found budgets:', budgetList.length)

        setBudgets(budgetList)

        console.log('🎉 DATABASE INTEGRATION COMPLETE!')
        console.log('✅ Loaded', budgetList.length, 'budgets from database')
        console.log('✅ All data is now persisted and retrieved from database!')

      } catch (error) {
        console.error('❌ Error loading budget data:', error)

        // Fallback to localStorage if API fails (for development)
        try {
          console.log('🔄 Falling back to localStorage...')
          const storedBudgets = localStorage.getItem('budgets')
          if (storedBudgets) {
            const budgetList = JSON.parse(storedBudgets)
            const transformedBudgets = budgetList.map((budget: any) => ({
              id: budget.id,
              name: budget.name,
              startDate: budget.startDate,
              endDate: budget.endDate,
              totalIncome: budget.totalIncome,
              totalAllocated: budget.budgetCategories?.reduce((sum: number, cat: any) => sum + cat.amount, 0) || 0,
              totalSpent: budget.budgetCategories?.reduce((sum: number, cat: any) => sum + cat.spent, 0) || 0,
              status: 'active',
              createdAt: budget.createdAt,
              categories: budget.budgetCategories?.length || 0
            }))
            setBudgets(transformedBudgets)
            console.log('✅ Loaded from localStorage fallback')
          } else {
            setBudgets([])
          }
        } catch (fallbackError) {
          console.error('❌ Fallback also failed:', fallbackError)
          setBudgets([])
        }
      } finally {
        setLoading(false)
      }
    }

    loadBudgetData()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-50 text-green-700 border-green-200'
      case 'completed': return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'draft': return 'bg-orange-50 text-orange-700 border-orange-200'
      default: return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Icons.Play className="w-4 h-4" />
      case 'completed': return <Icons.CheckCircle className="w-4 h-4" />
      case 'draft': return <Icons.Edit className="w-4 h-4" />
      default: return <Icons.Circle className="w-4 h-4" />
    }
  }

  if (loading) {
    return (
      <AppLayout>
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-3xl flex items-center justify-center mx-auto animate-pulse">
              <Icons.Folder className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-slate-900">Loading Budgets...</h2>
            <p className="text-slate-600">Preparing your budget overview</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 -m-6">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(59,130,246,0.15)_1px,transparent_0)] bg-[length:24px_24px] opacity-30 pointer-events-none"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12">

          {/* Header Section */}
          <div className="space-y-8 mb-12">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm text-slate-500">
              <span className="text-slate-900 font-semibold">Budget Management</span>
            </nav>

            {/* Main Header */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-6 lg:space-y-0">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-3xl flex items-center justify-center shadow-xl shadow-blue-600/25">
                    <Icons.Folder className="w-8 h-8 text-white" strokeWidth={1.5} />
                  </div>
                  <div>
                    <h1 className="text-4xl lg:text-5xl font-bold text-gradient tracking-tight">
                      My Budgets
                    </h1>
                    <p className="text-lg text-slate-600 font-medium">
                      Manage and track all your financial plans
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center space-x-3">
                <Button
                  onClick={() => router.push('/budget/create')}
                  className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Icons.Plus className="w-5 h-5" />
                  <span>Create New Budget</span>
                </Button>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Icons.Download className="w-4 h-4" />
                  <span>Export</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Budget Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Icons.Folder className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-900">{budgets.length}</div>
                    <div className="text-sm text-blue-600 font-medium">Total Budgets</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <Icons.Play className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-900">
                      {budgets.filter(b => b.status === 'active').length}
                    </div>
                    <div className="text-sm text-green-600 font-medium">Active</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <Icons.CheckCircle className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-900">
                      {budgets.filter(b => b.status === 'completed').length}
                    </div>
                    <div className="text-sm text-purple-600 font-medium">Completed</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <Icons.DollarSign className="w-6 h-6 text-orange-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-900">
                      ${budgets.reduce((sum, b) => sum + b.totalIncome, 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-orange-600 font-medium">Total Income</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          {/* Budget List */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-slate-900">Recent Budgets</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {budgets.map((budget, index) => (
                <Card
                  key={budget.id}
                  className="bg-white border-2 border-slate-200 hover:border-blue-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                  onClick={() => router.push(`/budget/view/${budget.id}`)}
                  style={{
                    animation: `slideInUp 0.6s ease-out ${index * 0.1}s both`
                  }}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-bold text-slate-900 truncate">
                        {budget.name}
                      </CardTitle>
                      <Badge variant="outline" className={`${getStatusColor(budget.status)} font-semibold`}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(budget.status)}
                          <span className="capitalize">{budget.status}</span>
                        </div>
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-500">
                      {new Date(budget.startDate).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                      })} - {new Date(budget.endDate).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                    </p>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Budget Overview */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded-xl">
                        <div className="text-lg font-bold text-blue-600">
                          ${budget.totalIncome.toLocaleString()}
                        </div>
                        <div className="text-xs text-blue-600 font-medium">Income</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-xl">
                        <div className="text-lg font-bold text-green-600">
                          ${budget.totalSpent.toLocaleString()}
                        </div>
                        <div className="text-xs text-green-600 font-medium">Spent</div>
                      </div>
                    </div>

                    {/* Progress */}
                    {budget.totalAllocated > 0 && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-600">Progress</span>
                          <span className="font-medium">
                            {Math.round((budget.totalSpent / budget.totalAllocated) * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-slate-200 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
                            style={{
                              width: `${Math.min((budget.totalSpent / budget.totalAllocated) * 100, 100)}%`
                            }}
                          />
                        </div>
                      </div>
                    )}

                    {/* Quick Info */}
                    <div className="flex items-center justify-between text-sm text-slate-500">
                      <span>{budget.categories} categories</span>
                      <span>Created {new Date(budget.createdAt).toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

        </div>
      </div>
    </AppLayout>
  )
}
