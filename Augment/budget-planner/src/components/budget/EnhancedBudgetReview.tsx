'use client'

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import * as Icons from 'lucide-react'

interface BudgetReviewProps {
  budgetData: any
  onBack: () => void
  onFinalize: () => void
}

// Enhanced Budget Review Component (Phase 2)
export default function EnhancedBudgetReview({ budgetData, onBack, onFinalize }: BudgetReviewProps) {
  const [showWhatIfModal, setShowWhatIfModal] = useState(false)
  const [whatIfIncome, setWhatIfIncome] = useState(budgetData.totalMonthlyIncome || 5000)
  const [insights, setInsights] = useState([])
  const [isAnimating, setIsAnimating] = useState(false)

  // Get dynamic categories from budget data
  const getCategories = useCallback(() => {
    if (!budgetData.selectedTemplate) return []
    
    const template = budgetData.selectedTemplate
    const monthlyIncome = budgetData.totalMonthlyIncome || 5000

    // Handle custom strategy with custom categories
    if (template.isCustom && template.customCategories) {
      return template.customCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        color: cat.color,
        percentage: cat.percentage,
        amount: Math.round((monthlyIncome * cat.percentage) / 100),
        subcategories: budgetData.categories?.find(c => c.id === cat.id)?.subcategories || []
      }))
    }

    // Handle standard strategies
    if (template.id === 'pay-yourself-first') {
      return [
        {
          id: 'savings',
          name: 'SAVINGS',
          description: 'Emergency fund, retirement, investments',
          color: 'green',
          percentage: 20,
          amount: Math.round((monthlyIncome * 20) / 100),
          subcategories: budgetData.categories?.find(c => c.id === 'savings')?.subcategories || []
        },
        {
          id: 'needs',
          name: 'NEEDS',
          description: 'All essential and lifestyle expenses',
          color: 'blue',
          percentage: 80,
          amount: Math.round((monthlyIncome * 80) / 100),
          subcategories: budgetData.categories?.find(c => c.id === 'needs')?.subcategories || []
        }
      ]
    }

    // Default 50/30/20 strategy
    return [
      {
        id: 'needs',
        name: 'NEEDS',
        description: 'Essential expenses you cannot avoid',
        color: 'blue',
        percentage: template.breakdown?.needs || 50,
        amount: Math.round((monthlyIncome * (template.breakdown?.needs || 50)) / 100),
        subcategories: budgetData.categories?.find(c => c.id === 'needs')?.subcategories || []
      },
      {
        id: 'wants',
        name: 'WANTS',
        description: 'Things you enjoy but could live without',
        color: 'orange',
        percentage: template.breakdown?.wants || 30,
        amount: Math.round((monthlyIncome * (template.breakdown?.wants || 30)) / 100),
        subcategories: budgetData.categories?.find(c => c.id === 'wants')?.subcategories || []
      },
      {
        id: 'smile',
        name: 'SMILE',
        description: 'Building your financial future',
        color: 'green',
        percentage: template.breakdown?.smile || template.breakdown?.savings || 20,
        amount: Math.round((monthlyIncome * (template.breakdown?.smile || template.breakdown?.savings || 20)) / 100),
        subcategories: budgetData.categories?.find(c => c.id === 'smile')?.subcategories || []
      }
    ]
  }, [budgetData.selectedTemplate, budgetData.totalMonthlyIncome, budgetData.categories])

  const categories = useMemo(() => getCategories(), [getCategories])
  const totalAllocated = useMemo(() => categories.reduce((sum, cat) => sum + cat.amount, 0), [categories])
  const remaining = useMemo(() => (budgetData.totalMonthlyIncome || 5000) - totalAllocated, [budgetData.totalMonthlyIncome, totalAllocated])

  // Generate AI-powered insights
  const generateInsights = useCallback(() => {
    const insights = []

    // Housing cost analysis
    const housingCategory = categories.find(cat => 
      cat.name.toLowerCase().includes('need') || 
      cat.name.toLowerCase().includes('housing')
    )
    if (housingCategory && housingCategory.percentage > 50) {
      insights.push({
        type: 'warning',
        icon: 'AlertTriangle',
        title: 'High Housing Costs',
        message: `Your ${housingCategory.name.toLowerCase()} allocation (${housingCategory.percentage}%) exceeds the recommended 50%. Consider optimizing to improve financial flexibility.`,
        action: 'Optimize Housing'
      })
    } else if (housingCategory && housingCategory.percentage <= 30) {
      insights.push({
        type: 'success',
        icon: 'CheckCircle',
        title: 'Excellent Housing Ratio',
        message: `Your ${housingCategory.name.toLowerCase()} allocation (${housingCategory.percentage}%) is well within recommended limits. Great job!`,
        action: 'Maintain Strategy'
      })
    }

    // Savings analysis
    const savingsCategory = categories.find(cat => 
      cat.name.toLowerCase().includes('saving') || 
      cat.name.toLowerCase().includes('smile') ||
      cat.name.toLowerCase().includes('future')
    )
    if (savingsCategory && savingsCategory.percentage < 15) {
      insights.push({
        type: 'info',
        icon: 'TrendingUp',
        title: 'Boost Your Savings',
        message: `Consider increasing your ${savingsCategory.name.toLowerCase()} allocation to at least 20% for better financial security.`,
        action: 'Increase Savings'
      })
    } else if (savingsCategory && savingsCategory.percentage >= 25) {
      insights.push({
        type: 'success',
        icon: 'Target',
        title: 'Excellent Savings Rate',
        message: `Your ${savingsCategory.percentage}% savings rate puts you on track for financial independence. Keep it up!`,
        action: 'Stay on Track'
      })
    }

    // Emergency fund calculation
    const emergencyFundNeeded = Math.round((totalAllocated * 6) / 12) // 6 months of expenses
    insights.push({
      type: 'info',
      icon: 'Shield',
      title: 'Emergency Fund Goal',
      message: `Based on your budget, aim for $${emergencyFundNeeded.toLocaleString()} in emergency savings (6 months of expenses).`,
      action: 'Plan Emergency Fund'
    })

    // Budget balance analysis
    if (remaining > 0) {
      insights.push({
        type: 'success',
        icon: 'DollarSign',
        title: 'Budget Surplus',
        message: `You have $${remaining.toLocaleString()} unallocated. Consider directing this toward your highest priority goal.`,
        action: 'Allocate Surplus'
      })
    } else if (remaining < 0) {
      insights.push({
        type: 'warning',
        icon: 'AlertCircle',
        title: 'Over Budget',
        message: `Your allocations exceed income by $${Math.abs(remaining).toLocaleString()}. Review and adjust your categories.`,
        action: 'Rebalance Budget'
      })
    }

    return insights
  }, [categories, budgetData.totalMonthlyIncome, totalAllocated, remaining])

  useEffect(() => {
    setInsights(generateInsights())
  }, [generateInsights])



  // What-if analysis
  const calculateWhatIf = () => {
    return categories.map(cat => ({
      ...cat,
      whatIfAmount: Math.round((whatIfIncome * cat.percentage) / 100),
      difference: Math.round((whatIfIncome * cat.percentage) / 100) - cat.amount
    }))
  }

  const getCategoryIcon = (categoryName) => {
    const name = categoryName.toLowerCase()
    if (name.includes('need') || name.includes('housing')) return 'Home'
    if (name.includes('want') || name.includes('lifestyle')) return 'ShoppingCart'
    if (name.includes('saving') || name.includes('smile') || name.includes('future')) return 'TrendingUp'
    if (name.includes('transport')) return 'Car'
    if (name.includes('food') || name.includes('dining')) return 'Coffee'
    return 'Folder' // Default to a valid lucide-react icon
  }

  return (
    <>
      {/* Success Animation Overlay */}
      {isAnimating && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-fadeIn">
          <div className="bg-white rounded-3xl p-12 text-center shadow-2xl animate-scaleIn">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 animate-bounce">
              <Icons.Check className="w-10 h-10 text-green-600" strokeWidth={3} />
            </div>
            <h3 className="text-2xl font-bold text-slate-900 mb-2">Template Saved!</h3>
            <p className="text-slate-600">Your budget template has been saved successfully.</p>
          </div>
        </div>
      )}

      <div className="space-y-12">
        {/* World-Class Header */}
        <div className="text-center space-y-8" style={{ animation: 'fadeInScale 0.6s ease-out' }}>
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-green-600 to-emerald-700 rounded-3xl shadow-2xl shadow-green-600/30 mb-8 animate-float">
            <Icons.CheckCircle className="w-12 h-12 text-white" strokeWidth={1.5} />
          </div>
          <div className="space-y-6">
            <h2 className="text-5xl lg:text-6xl font-bold text-gradient tracking-tight">
              Budget Review
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-medium">
              Review your personalized budget, get AI-powered insights, and save your strategy as a template for future use.
              Your financial plan is ready to help you achieve your goals.
            </p>
          </div>

          {/* Enhanced Progress Indicator */}
          <div className="flex items-center justify-center space-x-4 text-base text-slate-500">
            <div className="flex items-center space-x-3 bg-green-50 px-4 py-2 rounded-xl border border-green-200">
              <div className="w-3 h-3 bg-gradient-to-br from-green-500 to-green-600 rounded-full shadow-sm"></div>
              <span className="font-semibold text-green-700">Step 5 of 5</span>
            </div>
            <div className="w-2 h-2 bg-slate-300 rounded-full"></div>
            <span className="font-medium">Final Review</span>
          </div>
        </div>

        {/* Budget Summary Cards */}
        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold text-blue-700 flex items-center space-x-2">
                <Icons.DollarSign className="w-5 h-5" />
                <span>Total Income</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-900 mb-2">
                ${(budgetData.totalMonthlyIncome || 5000).toLocaleString()}
              </div>
              <div className="text-sm text-blue-600 font-medium">per month</div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold text-purple-700 flex items-center space-x-2">
                <Icons.PieChart className="w-5 h-5" />
                <span>Total Allocated</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-900 mb-2">
                ${totalAllocated.toLocaleString()}
              </div>
              <div className="text-sm text-purple-600 font-medium">
                {Math.round((totalAllocated / (budgetData.totalMonthlyIncome || 5000)) * 100)}% of income
              </div>
            </CardContent>
          </Card>

          <Card className={`bg-gradient-to-br border-2 shadow-lg hover:shadow-xl transition-all duration-300 ${
            remaining >= 0
              ? 'from-green-50 to-emerald-50 border-green-200'
              : 'from-red-50 to-rose-50 border-red-200'
          }`}>
            <CardHeader className="pb-3">
              <CardTitle className={`text-lg font-semibold flex items-center space-x-2 ${
                remaining >= 0 ? 'text-green-700' : 'text-red-700'
              }`}>
                {remaining >= 0 ? <Icons.Plus className="w-5 h-5" /> : <Icons.Minus className="w-5 h-5" />}
                <span>{remaining >= 0 ? 'Remaining' : 'Over Budget'}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`text-3xl font-bold mb-2 ${
                remaining >= 0 ? 'text-green-900' : 'text-red-900'
              }`}>
                ${Math.abs(remaining).toLocaleString()}
              </div>
              <div className={`text-sm font-medium ${
                remaining >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {remaining >= 0 ? 'Available to allocate' : 'Needs adjustment'}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Dynamic Category Breakdown */}
        <div className="max-w-6xl mx-auto">
          <h3 className="text-3xl font-bold text-slate-900 mb-8 text-center">Category Breakdown</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => {
              const IconComponent = Icons[getCategoryIcon(category.name)] || Icons.Folder

              return (
                <Card
                  key={category.id}
                  className="bg-white border-2 border-slate-200 hover:border-purple-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                  style={{
                    animation: `slideInUp 0.6s ease-out ${index * 0.1}s both`
                  }}
                >
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-12 h-12 bg-${category.color}-100 rounded-xl flex items-center justify-center`}>
                          <IconComponent className={`w-6 h-6 text-${category.color}-600`} strokeWidth={2} />
                        </div>
                        <div>
                          <div className="text-lg font-bold text-slate-900">{category.name}</div>
                          <div className="text-sm text-slate-500">{category.description}</div>
                        </div>
                      </div>
                      <Badge variant="outline" className={`bg-${category.color}-50 text-${category.color}-700 border-${category.color}-200 font-bold`}>
                        {category.percentage}%
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className={`text-2xl font-bold text-${category.color}-600 mb-1`}>
                          ${category.amount.toLocaleString()}
                        </div>
                        <div className="text-sm text-slate-500 font-medium">per month</div>
                      </div>

                      {category.subcategories && category.subcategories.length > 0 && (
                        <div className="space-y-2">
                          <div className="text-sm font-semibold text-slate-700">Subcategories:</div>
                          <div className="space-y-1">
                            {category.subcategories.slice(0, 3).map((sub, idx) => (
                              <div key={idx} className="flex justify-between text-sm">
                                <span className="text-slate-600">{sub.name}</span>
                                <span className="font-medium text-slate-700">${sub.budgetAmount?.toLocaleString()}</span>
                              </div>
                            ))}
                            {category.subcategories.length > 3 && (
                              <div className="text-xs text-slate-500 text-center">
                                +{category.subcategories.length - 3} more
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Smart Insights Engine */}
        <div className="max-w-6xl mx-auto">
          <h3 className="text-3xl font-bold text-slate-900 mb-8 text-center">Smart Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {insights.map((insight, index) => {
              const IconComponent = Icons[insight.icon] || Icons.Info
              const colorClasses = {
                success: 'bg-green-50 border-green-200 text-green-700',
                warning: 'bg-amber-50 border-amber-200 text-amber-700',
                info: 'bg-blue-50 border-blue-200 text-blue-700',
                danger: 'bg-red-50 border-red-200 text-red-700'
              }

              return (
                <Card
                  key={index}
                  className={`border-2 shadow-lg hover:shadow-xl transition-all duration-300 ${colorClasses[insight.type]}`}
                  style={{
                    animation: `slideInLeft 0.6s ease-out ${index * 0.1}s both`
                  }}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        insight.type === 'success' ? 'bg-green-100' :
                        insight.type === 'warning' ? 'bg-amber-100' :
                        insight.type === 'info' ? 'bg-blue-100' :
                        'bg-red-100'
                      }`}>
                        <IconComponent className={`w-6 h-6 ${
                          insight.type === 'success' ? 'text-green-600' :
                          insight.type === 'warning' ? 'text-amber-600' :
                          insight.type === 'info' ? 'text-blue-600' :
                          'text-red-600'
                        }`} strokeWidth={2} />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-bold mb-2">{insight.title}</h4>
                        <p className="text-sm leading-relaxed mb-4">{insight.message}</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className={`${
                            insight.type === 'success' ? 'border-green-300 text-green-700 hover:bg-green-100' :
                            insight.type === 'warning' ? 'border-amber-300 text-amber-700 hover:bg-amber-100' :
                            insight.type === 'info' ? 'border-blue-300 text-blue-700 hover:bg-blue-100' :
                            'border-red-300 text-red-700 hover:bg-red-100'
                          }`}
                        >
                          {insight.action}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Action Buttons - Simplified per UX Panel Recommendation */}
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* What-If Analysis */}
            <Dialog open={showWhatIfModal} onOpenChange={setShowWhatIfModal}>
              <DialogTrigger asChild>
                <Card className="cursor-pointer bg-gradient-to-br from-blue-50 to-cyan-50 border-2 border-blue-200 hover:border-blue-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Icons.Calculator className="w-8 h-8 text-blue-600" strokeWidth={2} />
                    </div>
                    <h3 className="text-xl font-bold text-blue-900 mb-2">What-If Analysis</h3>
                    <p className="text-blue-600 text-sm">Explore different scenarios</p>
                  </CardContent>
                </Card>
              </DialogTrigger>

              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="text-2xl font-bold text-slate-900 flex items-center space-x-2">
                    <Icons.Calculator className="w-6 h-6 text-blue-600" />
                    <span>What-If Analysis</span>
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-6 pt-4">
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-slate-700">Adjusted Monthly Income</label>
                    <div className="flex items-center space-x-4">
                      <Input
                        type="number"
                        value={whatIfIncome}
                        onChange={(e) => setWhatIfIncome(Number(e.target.value))}
                        className="bg-slate-50 border-2 border-slate-200 focus:border-blue-500 focus:bg-white rounded-xl px-4 py-3"
                      />
                      <div className="text-sm text-slate-500">
                        {whatIfIncome > (budgetData.totalMonthlyIncome || 5000) ? (
                          <span className="text-green-600 font-medium">
                            +${(whatIfIncome - (budgetData.totalMonthlyIncome || 5000)).toLocaleString()} increase
                          </span>
                        ) : whatIfIncome < (budgetData.totalMonthlyIncome || 5000) ? (
                          <span className="text-red-600 font-medium">
                            -${((budgetData.totalMonthlyIncome || 5000) - whatIfIncome).toLocaleString()} decrease
                          </span>
                        ) : (
                          <span className="text-slate-500">No change</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="text-lg font-bold text-slate-900">Impact on Categories</h4>
                    <div className="space-y-3">
                      {calculateWhatIf().map((category) => (
                        <div key={category.id} className="flex items-center justify-between p-4 bg-slate-50 rounded-xl">
                          <div className="flex items-center space-x-3">
                            <div className={`w-3 h-3 bg-${category.color}-500 rounded-full`}></div>
                            <span className="font-medium text-slate-900">{category.name}</span>
                            <span className="text-sm text-slate-500">({category.percentage}%)</span>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-slate-900">
                              ${category.whatIfAmount.toLocaleString()}
                            </div>
                            {category.difference !== 0 && (
                              <div className={`text-sm font-medium ${
                                category.difference > 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {category.difference > 0 ? '+' : ''}${category.difference.toLocaleString()}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    onClick={() => setShowWhatIfModal(false)}
                    className="w-full"
                  >
                    Close Analysis
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            {/* Finalize Budget - Primary Action */}
            <Card
              className="cursor-pointer bg-gradient-to-br from-green-50 to-emerald-50 border-4 border-green-300 hover:border-green-400 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 ring-4 ring-green-100"
              onClick={onFinalize}
            >
              <CardContent className="p-10 text-center">
                <div className="w-20 h-20 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Icons.CheckCircle className="w-10 h-10 text-green-600" strokeWidth={2.5} />
                </div>
                <h3 className="text-2xl font-bold text-green-900 mb-3">Finalize Budget</h3>
                <p className="text-green-700 text-base font-medium">Complete your budget setup</p>
                <div className="mt-4 px-4 py-2 bg-green-100 rounded-lg">
                  <p className="text-green-800 text-sm font-semibold">Ready to save your budget!</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Navigation */}
        <div className="max-w-4xl mx-auto flex justify-between items-center pt-8">
          <Button
            variant="outline"
            onClick={onBack}
            className="flex items-center space-x-2 px-8 py-3 text-lg font-semibold border-2 border-slate-300 hover:border-slate-400 rounded-xl"
          >
            <Icons.ArrowLeft className="w-5 h-5" />
            <span>Back to Categories</span>
          </Button>

          <div className="text-center">
            <div className="text-sm text-slate-500 mb-2">Budget Creation Progress</div>
            <div className="w-64 bg-slate-200 rounded-full h-3 overflow-hidden">
              <div className="bg-gradient-to-r from-green-500 to-emerald-600 h-3 rounded-full w-full transition-all duration-1000 shadow-lg">
                <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse"></div>
              </div>
            </div>
            <div className="text-sm font-semibold text-green-600 mt-2">100% Complete</div>
          </div>

          <Button
            onClick={onFinalize}
            className="flex items-center space-x-2 px-8 py-3 text-lg font-semibold bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <span>Complete Budget</span>
            <Icons.ArrowRight className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </>
  )
}
