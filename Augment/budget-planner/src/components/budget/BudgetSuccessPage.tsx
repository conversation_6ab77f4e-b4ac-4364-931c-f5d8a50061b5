'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { CheckCircle, Eye, Plus, Save, Sparkles } from 'lucide-react'

interface BudgetSuccessPageProps {
  budgetData: any
  onViewBudget: () => void
  onCreateAnother: () => void
  createdBudgetId?: string | null
  isCreating?: boolean
  creationError?: string | null
}

export default function BudgetSuccessPage({
  budgetData,
  onViewBudget,
  onCreateAnother,
  createdBudgetId,
  isCreating,
  creationError
}: BudgetSuccessPageProps) {
  const router = useRouter()
  const [showTemplateModal, setShowTemplateModal] = useState(false)
  const [templateName, setTemplateName] = useState('')
  const [templateDescription, setTemplateDescription] = useState('')
  const [templateTags, setTemplateTags] = useState<string[]>([])
  const [isAnimating, setIsAnimating] = useState(false)

  // Auto-generate template name
  const generateTemplateName = () => {
    const strategy = budgetData.selectedTemplate?.name || 'Custom Strategy'
    const income = budgetData.totalMonthlyIncome || 5000
    const incomeRange = income < 3000 ? 'Starter' : income < 6000 ? 'Standard' : 'Premium'
    return `${incomeRange} ${strategy} - ${new Date().toLocaleDateString()}`
  }

  // Save template functionality
  const saveTemplate = () => {
    const template = {
      id: `template_${Date.now()}`,
      name: templateName || generateTemplateName(),
      description: templateDescription,
      tags: templateTags,
      strategy: budgetData.selectedTemplate,
      categories: budgetData.categories,
      totalIncome: budgetData.totalMonthlyIncome,
      createdAt: new Date().toISOString(),
      version: '1.0'
    }

    // Save to localStorage (in production, this would be saved to a database)
    const savedTemplates = JSON.parse(localStorage.getItem('budgetTemplates') || '[]')
    savedTemplates.push(template)
    localStorage.setItem('budgetTemplates', JSON.stringify(savedTemplates))

    setShowTemplateModal(false)
    setTemplateName('')
    setTemplateDescription('')
    setTemplateTags([])

    // Show success animation
    setIsAnimating(true)
    setTimeout(() => setIsAnimating(false), 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 animate-bounce">
            <CheckCircle className="w-12 h-12 text-green-600" strokeWidth={2} />
          </div>
          <h1 className="text-4xl font-bold text-green-900 mb-4">
            🎉 Budget Created Successfully!
          </h1>
          <p className="text-lg text-green-700 mb-2">
            Your budget has been saved to the database and is ready to use
          </p>

          {/* Database Integration Status */}
          {createdBudgetId && (
            <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-4 mx-auto max-w-md">
              <div className="flex items-center justify-center space-x-2 text-green-700 mb-2">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-semibold">Database Integration Complete</span>
              </div>
              <p className="text-xs text-green-600">
                Budget ID: {createdBudgetId.slice(0, 8)}...
              </p>
            </div>
          )}

          {creationError && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-4 mx-auto max-w-md">
              <p className="text-sm text-red-700">
                ⚠️ {creationError}
              </p>
            </div>
          )}

          <div className="flex items-center justify-center space-x-2 text-green-600">
            <Sparkles className="w-5 h-5" />
            <span className="text-sm font-medium">
              Total Monthly Budget: ${(budgetData.totalMonthlyIncome || 0).toLocaleString()}
            </span>
            <Sparkles className="w-5 h-5" />
          </div>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* View Budget */}
          <Card
            className="cursor-pointer bg-gradient-to-br from-blue-50 to-cyan-50 border-2 border-blue-200 hover:border-blue-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
            onClick={onViewBudget}
          >
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Eye className="w-8 h-8 text-blue-600" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-2">View Budget</h3>
              <p className="text-blue-600 text-sm">See your complete budget breakdown</p>
            </CardContent>
          </Card>

          {/* Create Another */}
          <Card 
            className="cursor-pointer bg-gradient-to-br from-purple-50 to-indigo-50 border-2 border-purple-200 hover:border-purple-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
            onClick={onCreateAnother}
          >
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Plus className="w-8 h-8 text-purple-600" strokeWidth={2} />
              </div>
              <h3 className="text-xl font-bold text-purple-900 mb-2">Create Another</h3>
              <p className="text-purple-600 text-sm">Start a new budget from scratch</p>
            </CardContent>
          </Card>

          {/* Save as Template */}
          <Dialog open={showTemplateModal} onOpenChange={setShowTemplateModal}>
            <DialogTrigger asChild>
              <Card className="cursor-pointer bg-gradient-to-br from-orange-50 to-amber-50 border-2 border-orange-200 hover:border-orange-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Save className="w-8 h-8 text-orange-600" strokeWidth={2} />
                  </div>
                  <h3 className="text-xl font-bold text-orange-900 mb-2">Save as Template</h3>
                  <p className="text-orange-600 text-sm">Reuse this configuration later</p>
                </CardContent>
              </Card>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold text-slate-900 flex items-center space-x-2">
                  <Save className="w-6 h-6 text-orange-600" />
                  <span>Save Budget Template</span>
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-6 pt-4">
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-slate-700">Template Name</label>
                  <Input
                    value={templateName}
                    onChange={(e) => setTemplateName(e.target.value)}
                    placeholder={generateTemplateName()}
                    className="bg-slate-50 border-2 border-slate-200 focus:border-orange-500 focus:bg-white rounded-xl px-4 py-3"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-slate-700">Description (Optional)</label>
                  <Input
                    value={templateDescription}
                    onChange={(e) => setTemplateDescription(e.target.value)}
                    placeholder="Describe when to use this template..."
                    className="bg-slate-50 border-2 border-slate-200 focus:border-orange-500 focus:bg-white rounded-xl px-4 py-3"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-slate-700">Tags (Optional)</label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {templateTags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="bg-orange-100 text-orange-800">
                        {tag}
                        <button
                          onClick={() => setTemplateTags(templateTags.filter((_, i) => i !== index))}
                          className="ml-1 text-orange-600 hover:text-orange-800"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <Input
                    placeholder="Add tags (press Enter)"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                        setTemplateTags([...templateTags, e.currentTarget.value.trim()])
                        e.currentTarget.value = ''
                      }
                    }}
                    className="bg-slate-50 border-2 border-slate-200 focus:border-orange-500 focus:bg-white rounded-xl px-4 py-3"
                  />
                </div>
                <div className="flex space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowTemplateModal(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={saveTemplate}
                    className="flex-1 bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-700 hover:to-amber-700 text-white"
                  >
                    Save Template
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Success Animation */}
        {isAnimating && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 text-center animate-pulse">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-green-900 mb-2">Template Saved!</h3>
              <p className="text-green-700">Your budget template is ready for future use</p>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <Card className="bg-white/80 backdrop-blur-sm border-2 border-green-200">
          <CardContent className="p-6">
            <h3 className="text-lg font-bold text-green-900 mb-4 text-center">Budget Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-green-600">${(budgetData.totalMonthlyIncome || 0).toLocaleString()}</p>
                <p className="text-sm text-green-700">Monthly Income</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-600">{budgetData.categories?.length || 0}</p>
                <p className="text-sm text-blue-700">Categories</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-600">{budgetData.selectedTemplate?.name || 'Custom'}</p>
                <p className="text-sm text-purple-700">Strategy</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-orange-600">{budgetData.duration || 'Monthly'}</p>
                <p className="text-sm text-orange-700">Duration</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
