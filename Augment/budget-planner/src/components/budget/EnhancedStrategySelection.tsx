'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import * as Icons from 'lucide-react'

interface StrategySelectionProps {
  budgetData: any
  setBudgetData: (data: any) => void
  onNext: () => void
  onBack: () => void
}

// Enhanced Strategy Selection Component (Phase 2)
export default function EnhancedStrategySelection({ budgetData, setBudgetData, onNext, onBack }: StrategySelectionProps) {
  const [selectedStrategy, setSelectedStrategy] = useState(budgetData.selectedTemplate?.id || null)
  const [customCategories, setCustomCategories] = useState([
    { id: 'needs', name: 'NEEDS', percentage: 50, color: 'blue', description: 'Essential expenses you cannot avoid', editable: true },
    { id: 'wants', name: 'WANTS', percentage: 30, color: 'orange', description: 'Things you enjoy but could live without', editable: true },
    { id: 'smile', name: 'SMILE', percentage: 20, color: 'green', description: 'Building your financial future', editable: true }
  ])
  const [showCustomBuilder, setShowCustomBuilder] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const [validationErrors, setValidationErrors] = useState({})
  const [showAISuggestions, setShowAISuggestions] = useState(false)

  // Enhanced Budget Strategies (Phase 2)
  const strategies = [
    {
      id: '50-30-20',
      name: '50/30/20 Strategy',
      description: 'The gold standard for balanced budgeting',
      breakdown: { needs: 50, wants: 30, smile: 20 },
      categories: [
        { id: 'needs', name: 'NEEDS', percentage: 50, color: 'blue', description: 'Housing, groceries, utilities, transportation', editable: true },
        { id: 'wants', name: 'WANTS', percentage: 30, color: 'orange', description: 'Entertainment, dining, shopping, hobbies', editable: true },
        { id: 'smile', name: 'SMILE', percentage: 20, color: 'green', description: 'Savings, investments, debt payoff', editable: true }
      ],
      bestFor: 'Simplicity & balance',
      focusArea: 'Proportional spending',
      color: 'blue',
      icon: 'PieChart',
      popularity: 85,
      recommended: true
    },
    {
      id: 'pay-yourself-first',
      name: 'Pay Yourself First',
      description: 'Prioritize your future before everything else',
      breakdown: { savings: 20, needs: 80 },
      categories: [
        { id: 'savings', name: 'SAVINGS', percentage: 20, color: 'green', description: 'Emergency fund, retirement, investments', editable: true },
        { id: 'needs', name: 'NEEDS', percentage: 80, color: 'blue', description: 'All essential and lifestyle expenses', editable: true }
      ],
      bestFor: 'Saving & investing automatically',
      focusArea: 'Prioritizing savings',
      color: 'green',
      icon: 'TrendingUp',
      popularity: 72,
      recommended: false
    },
    {
      id: 'custom',
      name: 'Custom Strategy',
      description: 'Build your personalized budget approach',
      breakdown: { custom: true },
      categories: customCategories,
      bestFor: 'Advanced users & unique situations',
      focusArea: 'Personalized allocation',
      color: 'purple',
      icon: 'Settings',
      popularity: 43,
      recommended: false,
      isCustom: true
    }
  ]

  // AI-Powered Allocation Suggestions
  const getAISuggestions = (strategyType) => {
    const suggestions = {
      balanced: [
        { name: 'NEEDS', percentage: 50, description: 'Essential living expenses' },
        { name: 'WANTS', percentage: 30, description: 'Lifestyle and entertainment' },
        { name: 'SMILE', percentage: 20, description: 'Savings and investments' }
      ],
      aggressive: [
        { name: 'SAVINGS', percentage: 30, description: 'High savings rate' },
        { name: 'NEEDS', percentage: 50, description: 'Minimized essentials' },
        { name: 'WANTS', percentage: 20, description: 'Limited lifestyle spending' }
      ],
      conservative: [
        { name: 'NEEDS', percentage: 60, description: 'Comfortable essentials' },
        { name: 'WANTS', percentage: 25, description: 'Moderate lifestyle' },
        { name: 'SAVINGS', percentage: 15, description: 'Steady savings' }
      ]
    }
    return suggestions[strategyType] || suggestions.balanced
  }

  // Handle strategy selection with animations
  const handleStrategySelect = (strategy) => {
    setIsAnimating(true)
    setSelectedStrategy(strategy.id)

    if (strategy.isCustom) {
      setShowCustomBuilder(true)
      // Save custom strategy with current categories
      setBudgetData({
        ...budgetData,
        selectedTemplate: {
          ...strategy,
          categories: customCategories,
          customCategories: customCategories
        }
      })
    } else {
      setShowCustomBuilder(false)
      setBudgetData({
        ...budgetData,
        selectedTemplate: strategy
      })
    }

    setTimeout(() => setIsAnimating(false), 300)
  }

  // Custom category management
  const updateCustomCategory = (categoryId, field, value) => {
    const updatedCategories = customCategories.map(cat =>
      cat.id === categoryId ? { ...cat, [field]: value } : cat
    )
    setCustomCategories(updatedCategories)

    // Update strategy if custom is selected
    if (selectedStrategy === 'custom') {
      const customStrategy = strategies.find(s => s.id === 'custom')
      setBudgetData({
        ...budgetData,
        selectedTemplate: {
          ...customStrategy,
          categories: updatedCategories,
          customCategories: updatedCategories // Add this for Step 4 compatibility
        }
      })
    }
  }

  const addCustomCategory = () => {
    const newCategory = {
      id: `custom_${Date.now()}`,
      name: 'New Category',
      percentage: 0,
      color: 'purple',
      description: 'Add your description here',
      editable: true
    }
    const updatedCategories = [...customCategories, newCategory]
    setCustomCategories(updatedCategories)

    // Update budgetData if custom strategy is selected
    if (selectedStrategy === 'custom') {
      const customStrategy = strategies.find(s => s.id === 'custom')
      setBudgetData({
        ...budgetData,
        selectedTemplate: {
          ...customStrategy,
          categories: updatedCategories,
          customCategories: updatedCategories
        }
      })
    }
  }

  const removeCustomCategory = (categoryId) => {
    if (customCategories.length > 2) {
      const updatedCategories = customCategories.filter(cat => cat.id !== categoryId)
      setCustomCategories(updatedCategories)

      // Update budgetData if custom strategy is selected
      if (selectedStrategy === 'custom') {
        const customStrategy = strategies.find(s => s.id === 'custom')
        setBudgetData({
          ...budgetData,
          selectedTemplate: {
            ...customStrategy,
            categories: updatedCategories,
            customCategories: updatedCategories
          }
        })
      }
    }
  }

  // Validation
  const getTotalPercentage = () => {
    return customCategories.reduce((sum, cat) => sum + cat.percentage, 0)
  }

  const validateSelection = () => {
    if (!selectedStrategy) return false
    if (selectedStrategy === 'custom' && getTotalPercentage() !== 100) return false
    return true
  }

  return (
    <>
      {/* Custom Slider Styles */}
      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #8B5CF6;
          cursor: pointer;
          border: 3px solid white;
          box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
          transition: all 0.2s ease;
        }
        .slider::-webkit-slider-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
        }
        .slider::-moz-range-thumb {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #8B5CF6;
          cursor: pointer;
          border: 3px solid white;
          box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
          transition: all 0.2s ease;
        }
        .slider::-moz-range-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
        }
      `}</style>

      <div className="space-y-12">
        {/* World-Class Header */}
      <div className="text-center space-y-8" style={{ animation: 'fadeInScale 0.6s ease-out' }}>
        <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-purple-600 to-indigo-700 rounded-3xl shadow-2xl shadow-purple-600/30 mb-8 animate-float">
          <Icons.Target className="w-12 h-12 text-white" strokeWidth={1.5} />
        </div>
        <div className="space-y-6">
          <h2 className="text-5xl lg:text-6xl font-bold text-gradient tracking-tight">
            Choose Your Strategy
          </h2>
          <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-medium">
            Select a proven budgeting framework that aligns with your financial goals and lifestyle preferences.
            Each strategy is designed by financial experts to help you achieve different objectives.
          </p>
        </div>

        {/* Enhanced Progress Indicator */}
        <div className="flex items-center justify-center space-x-4 text-base text-slate-500">
          <div className="flex items-center space-x-3 bg-purple-50 px-4 py-2 rounded-xl border border-purple-200">
            <div className="w-3 h-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full shadow-sm"></div>
            <span className="font-semibold text-purple-700">Step 3 of 5</span>
          </div>
          <div className="w-2 h-2 bg-slate-300 rounded-full"></div>
          <span className="font-medium">Budget Strategy</span>
        </div>
      </div>

      {/* Strategy Cards Grid */}
      <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
        {strategies.map((strategy, index) => {
          const IconComponent = Icons[strategy.icon]
          const isSelected = selectedStrategy === strategy.id

          return (
            <div
              key={strategy.id}
              onClick={() => handleStrategySelect(strategy)}
              className={`relative cursor-pointer rounded-3xl p-8 border-2 transition-all duration-500 hover:shadow-2xl group transform hover:-translate-y-2 ${
                isSelected
                  ? 'border-purple-500 bg-gradient-to-br from-purple-50 to-indigo-50 shadow-2xl shadow-purple-500/25 scale-105'
                  : 'border-slate-200 bg-white hover:border-purple-300 hover:bg-gradient-to-br hover:from-slate-50 hover:to-purple-50/30 hover:shadow-purple-500/10'
              }`}
              style={{
                animation: `slideInUp 0.6s ease-out ${index * 0.15}s both`,
                transformOrigin: 'center bottom'
              }}
            >
              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute -top-2 -right-2 w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl shadow-purple-500/40 animate-pulse">
                  <Icons.Check className="w-6 h-6 text-white" strokeWidth={3} />
                </div>
              )}

              {/* Strategy Content */}
              <div className="relative space-y-8">
                <div className="space-y-6">
                  <div className="relative">
                    <div className={`w-20 h-20 bg-gradient-to-br from-${strategy.color}-500 to-${strategy.color}-600 rounded-3xl flex items-center justify-center shadow-xl shadow-${strategy.color}-500/30 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 ${
                      isSelected ? 'animate-pulse' : ''
                    }`}>
                      <IconComponent className="w-10 h-10 text-white" strokeWidth={1.5} />
                    </div>
                    {strategy.recommended && (
                      <div className="absolute -top-2 -right-2 bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg animate-bounce">
                        ⭐ Recommended
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <h3 className="text-2xl font-bold text-slate-900 group-hover:text-purple-700 transition-colors duration-300">
                        {strategy.name}
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="outline" className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200 text-sm font-semibold px-3 py-1">
                          {strategy.bestFor}
                        </Badge>
                        <Badge variant="outline" className="bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 text-sm font-semibold px-3 py-1">
                          {strategy.popularity}% adoption
                        </Badge>
                      </div>
                    </div>
                    <p className="text-base text-slate-600 leading-relaxed font-medium">{strategy.description}</p>
                  </div>
                </div>

                {/* Allocation Preview */}
                <div className="space-y-4">
                  <h4 className="text-lg font-bold text-slate-900 flex items-center space-x-2">
                    <Icons.BarChart3 className="w-5 h-5 text-purple-600" strokeWidth={2} />
                    <span>Allocation</span>
                  </h4>
                  <div className="space-y-3">
                    {strategy.categories.map((category) => (
                      <div key={category.id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 bg-${category.color}-500 rounded-full`}></div>
                          <span className="text-sm font-semibold text-slate-700">{category.name}</span>
                        </div>
                        <span className={`text-lg font-bold text-${category.color}-600 bg-${category.color}-50 px-3 py-1 rounded-lg`}>
                          {category.percentage}%
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Custom Strategy Builder */}
      {showCustomBuilder && selectedStrategy === 'custom' && (
        <div className="max-w-4xl mx-auto bg-gradient-to-br from-purple-50 to-indigo-50 rounded-3xl p-8 border-2 border-purple-200 shadow-xl">
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <h3 className="text-3xl font-bold text-purple-900">Custom Strategy Builder</h3>
              <p className="text-lg text-purple-700">Create your personalized budget allocation</p>
            </div>

            {/* Custom Categories */}
            <div className="space-y-6">
              {customCategories.map((category, index) => (
                <div key={category.id} className="bg-white rounded-2xl p-8 border border-purple-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="space-y-6">
                    {/* Category Name Input */}
                    <div className="flex items-center justify-between">
                      <div className="flex-1 mr-4">
                        <label className="block text-sm font-semibold text-slate-700 mb-2">
                          Category Name
                        </label>
                        <Input
                          value={category.name}
                          onChange={(e) => updateCustomCategory(category.id, 'name', e.target.value)}
                          className="w-full px-4 py-3 text-lg font-semibold bg-slate-50 border-2 border-slate-200 rounded-xl focus:border-purple-500 focus:bg-white transition-all duration-200 placeholder:text-slate-400"
                          placeholder="Enter category name"
                        />
                      </div>
                      {customCategories.length > 2 && (
                        <Button
                          onClick={() => removeCustomCategory(category.id)}
                          variant="ghost"
                          size="sm"
                          className="mt-7 text-red-500 hover:text-red-700 hover:bg-red-50 p-2 rounded-lg"
                        >
                          <Icons.X className="w-5 h-5" />
                        </Button>
                      )}
                    </div>

                    {/* Category Description Input */}
                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-2">
                        Description
                      </label>
                      <Input
                        value={category.description}
                        onChange={(e) => updateCustomCategory(category.id, 'description', e.target.value)}
                        className="w-full px-4 py-3 text-base bg-slate-50 border-2 border-slate-200 rounded-xl focus:border-purple-500 focus:bg-white transition-all duration-200 placeholder:text-slate-400"
                        placeholder="Describe what this category includes..."
                      />
                    </div>

                    {/* Percentage Allocation */}
                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-3">
                        Allocation Percentage
                      </label>
                      <div className="flex items-center space-x-4">
                        <div className="flex-1">
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={category.percentage}
                            onChange={(e) => updateCustomCategory(category.id, 'percentage', parseInt(e.target.value))}
                            className="w-full h-3 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                            style={{
                              background: `linear-gradient(to right, #8B5CF6 0%, #8B5CF6 ${category.percentage}%, #E2E8F0 ${category.percentage}%, #E2E8F0 100%)`
                            }}
                          />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            value={category.percentage}
                            onChange={(e) => updateCustomCategory(category.id, 'percentage', Math.min(100, Math.max(0, parseInt(e.target.value) || 0)))}
                            className="w-20 px-3 py-2 text-center text-lg font-bold bg-slate-50 border-2 border-slate-200 rounded-xl focus:border-purple-500 focus:bg-white transition-all duration-200"
                          />
                          <span className="text-lg font-bold text-slate-700">%</span>
                        </div>
                      </div>
                    </div>

                    {/* Monthly Amount Display */}
                    <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-200 text-center">
                      <div className="text-3xl font-bold text-purple-600 mb-1">
                        ${Math.round((budgetData.totalMonthlyIncome * category.percentage) / 100).toLocaleString()}
                      </div>
                      <div className="text-sm font-medium text-purple-500">per month</div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Add Category Button */}
              <Button
                onClick={addCustomCategory}
                variant="outline"
                className="w-full border-2 border-dashed border-purple-300 hover:border-purple-400 text-purple-700 hover:bg-purple-50 py-8 rounded-2xl text-lg font-semibold transition-all duration-300 hover:scale-[1.02]"
              >
                <Icons.Plus className="w-6 h-6 mr-3" strokeWidth={2} />
                Add New Category
              </Button>

              {/* Total Validation */}
              <div className={`rounded-2xl p-8 border-2 transition-all duration-500 ${
                getTotalPercentage() === 100
                  ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-300 shadow-green-100'
                  : getTotalPercentage() > 100
                  ? 'bg-gradient-to-br from-red-50 to-rose-50 border-red-300 shadow-red-100'
                  : 'bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-300 shadow-amber-100'
              } shadow-lg`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getTotalPercentage() === 100 ? (
                      <Icons.CheckCircle className="w-6 h-6 text-green-600" strokeWidth={2} />
                    ) : getTotalPercentage() > 100 ? (
                      <Icons.AlertCircle className="w-6 h-6 text-red-600" strokeWidth={2} />
                    ) : (
                      <Icons.Clock className="w-6 h-6 text-amber-600" strokeWidth={2} />
                    )}
                    <span className="text-xl font-bold text-slate-900">Total Allocation</span>
                  </div>
                  <div className="text-right">
                    <span className={`text-3xl font-bold px-6 py-3 rounded-xl ${
                      getTotalPercentage() === 100 ? 'text-green-600 bg-green-100' :
                      getTotalPercentage() > 100 ? 'text-red-600 bg-red-100' : 'text-amber-600 bg-amber-100'
                    }`}>
                      {getTotalPercentage()}%
                    </span>
                    <div className={`text-sm font-medium mt-1 ${
                      getTotalPercentage() === 100 ? 'text-green-600' :
                      getTotalPercentage() > 100 ? 'text-red-600' : 'text-amber-600'
                    }`}>
                      {getTotalPercentage() === 100 ? 'Perfect!' :
                       getTotalPercentage() > 100 ? 'Over budget' : 'Incomplete'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between pt-12">
        <Button
          variant="outline"
          onClick={onBack}
          className="group px-8 py-5 rounded-2xl text-lg font-semibold border-2 border-slate-300 hover:border-slate-400 hover:bg-slate-50 transition-all duration-300"
        >
          <div className="flex items-center space-x-3">
            <Icons.ArrowLeft className="w-5 h-5 group-hover:-translate-x-0.5 transition-transform duration-300" strokeWidth={2} />
            <span>Back to Income</span>
          </div>
        </Button>
        <Button
          onClick={onNext}
          disabled={!validateSelection()}
          className="group relative bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 hover:from-purple-700 hover:via-indigo-700 hover:to-blue-700 text-white px-12 py-5 rounded-2xl text-lg font-bold shadow-xl shadow-purple-600/25 hover:shadow-2xl hover:shadow-purple-600/30 transition-all duration-300 hover:scale-105 overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative flex items-center space-x-4">
            <span>Continue to Categories</span>
            <div className="flex items-center justify-center w-6 h-6 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300">
              <Icons.ArrowRight className="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300" strokeWidth={2.5} />
            </div>
          </div>
        </Button>
      </div>
      </div>
    </>
  )
}
