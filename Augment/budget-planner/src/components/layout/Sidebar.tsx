'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

const navigationItems = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <rect x="3" y="3" width="7" height="7" />
        <rect x="14" y="3" width="7" height="7" />
        <rect x="14" y="14" width="7" height="7" />
        <rect x="3" y="14" width="7" height="7" />
      </svg>
    )
  },
  {
    name: 'Budget',
    href: '/budget',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
        <line x1="16" y1="2" x2="16" y2="6" />
        <line x1="8" y1="2" x2="8" y2="6" />
        <line x1="3" y1="10" x2="21" y2="10" />
      </svg>
    )
  },
  {
    name: 'Income',
    href: '/income',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <line x1="12" y1="1" x2="12" y2="23" />
        <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
      </svg>
    )
  },
  {
    name: 'Expense',
    href: '/expenses',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <rect x="3" y="5" width="18" height="14" rx="2" />
        <line x1="3" y1="10" x2="21" y2="10" />
      </svg>
    )
  },
  {
    name: 'Goals',
    href: '/goals',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <circle cx="12" cy="12" r="10" />
        <circle cx="12" cy="12" r="6" />
        <circle cx="12" cy="12" r="2" />
      </svg>
    )
  },
  {
    name: 'Insights',
    href: '/analytics',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <line x1="18" y1="20" x2="18" y2="10" />
        <line x1="12" y1="20" x2="12" y2="4" />
        <line x1="6" y1="20" x2="6" y2="14" />
      </svg>
    )
  },
  {
    name: 'Notifications',
    href: '/notifications',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
        <path d="M13.73 21a2 2 0 0 1-3.46 0" />
      </svg>
    )
  }
]

const bottomNavigationItems = [
  {
    name: 'Setting',
    href: '/settings',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <circle cx="12" cy="12" r="3" />
        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
      </svg>
    )
  },
  {
    name: 'Help & Support',
    href: '/help',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
      </svg>
    )
  }
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()

  return (
    <div className={cn('flex h-full w-64 flex-col bg-white border-r', className)}
         style={{ borderColor: 'var(--color-gray-200)' }}>
      {/* 🎨 Enhanced Brand Identity - World-Class Design */}
      <div className="flex h-16 items-center px-6 border-b"
           style={{ borderColor: 'var(--color-gray-100)' }}>
        <div className="flex items-center">
          {/* Premium Logo with Enhanced Design */}
          <div className="relative">
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="18" cy="18" r="18" fill="var(--color-primary-500)" fillOpacity="0.1"/>
              <circle cx="18" cy="18" r="16" fill="var(--color-primary-500)" fillOpacity="0.05"/>
              <path d="M18 8C12.48 8 8 12.48 8 18C8 23.52 12.48 28 18 28C23.52 28 28 23.52 28 18C28 12.48 23.52 8 18 8ZM18 26C13.59 26 10 22.41 10 18C10 13.59 13.59 10 18 10C22.41 10 26 13.59 26 18C26 22.41 22.41 26 18 26Z" fill="var(--color-primary-600)"/>
              <path d="M19 13H17V19H23V17H19V13Z" fill="var(--color-primary-600)"/>
            </svg>
            {/* Subtle glow effect */}
            <div className="absolute inset-0 rounded-full opacity-20 pointer-events-none"
                 style={{ background: 'radial-gradient(circle, var(--color-primary-400) 0%, transparent 70%)' }}></div>
          </div>
          <div className="ml-3">
            <h1 className="text-heading-sm font-bold"
                style={{ color: 'var(--color-primary-700)' }}>
              BudgetTrack
            </h1>
            <div className="text-label-sm"
                 style={{ color: 'var(--color-gray-500)' }}>
              Financial Dashboard
            </div>
          </div>
        </div>
      </div>

      {/* 🧭 Enhanced Navigation - World-Class UX */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link key={item.name} href={item.href}>
              <div
                className={cn(
                  'group relative w-full flex items-center h-12 px-3 rounded-xl transition-all duration-300 cursor-pointer',
                  isActive
                    ? 'font-semibold shadow-sm'
                    : 'hover:shadow-sm font-medium'
                )}
                style={{
                  backgroundColor: isActive
                    ? 'var(--color-primary-50)'
                    : 'transparent',
                  color: isActive
                    ? 'var(--color-primary-700)'
                    : 'var(--color-gray-600)',
                  borderLeft: isActive
                    ? '3px solid var(--color-primary-600)'
                    : '3px solid transparent'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'var(--color-gray-50)'
                    e.currentTarget.style.color = 'var(--color-gray-700)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent'
                    e.currentTarget.style.color = 'var(--color-gray-600)'
                  }
                }}
              >
                {/* Active indicator */}
                {isActive && (
                  <div
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 rounded-r-full pointer-events-none"
                    style={{ backgroundColor: 'var(--color-primary-600)' }}
                  />
                )}

                {/* Icon */}
                <span className={cn(
                  'mr-3 transition-colors duration-300',
                  isActive ? 'opacity-100' : 'opacity-70 group-hover:opacity-100'
                )}
                style={{
                  color: isActive
                    ? 'var(--color-primary-600)'
                    : 'var(--color-gray-500)'
                }}>
                  {item.icon}
                </span>

                {/* Label */}
                <span className="text-body-md transition-colors duration-300">
                  {item.name}
                </span>

                {/* Hover effect */}
                <div className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none"
                     style={{ backgroundColor: 'var(--color-primary-600)' }} />
              </div>
            </Link>
          )
        })}
      </nav>

      {/* 🔧 Enhanced Bottom Navigation */}
      <div className="mt-auto px-4 py-6 space-y-2 border-t"
           style={{ borderColor: 'var(--color-gray-100)' }}>
        {bottomNavigationItems.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link key={item.name} href={item.href}>
              <div
                className={cn(
                  'group relative w-full flex items-center h-12 px-3 rounded-xl transition-all duration-300 cursor-pointer',
                  isActive
                    ? 'font-semibold shadow-sm'
                    : 'hover:shadow-sm font-medium'
                )}
                style={{
                  backgroundColor: isActive
                    ? 'var(--color-primary-50)'
                    : 'transparent',
                  color: isActive
                    ? 'var(--color-primary-700)'
                    : 'var(--color-gray-600)',
                  borderLeft: isActive
                    ? '3px solid var(--color-primary-600)'
                    : '3px solid transparent'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'var(--color-gray-50)'
                    e.currentTarget.style.color = 'var(--color-gray-700)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent'
                    e.currentTarget.style.color = 'var(--color-gray-600)'
                  }
                }}
              >
                {/* Active indicator */}
                {isActive && (
                  <div
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 rounded-r-full pointer-events-none"
                    style={{ backgroundColor: 'var(--color-primary-600)' }}
                  />
                )}

                {/* Icon */}
                <span className={cn(
                  'mr-3 transition-colors duration-300',
                  isActive ? 'opacity-100' : 'opacity-70 group-hover:opacity-100'
                )}
                style={{
                  color: isActive
                    ? 'var(--color-primary-600)'
                    : 'var(--color-gray-500)'
                }}>
                  {item.icon}
                </span>

                {/* Label */}
                <span className="text-body-md transition-colors duration-300">
                  {item.name}
                </span>

                {/* Hover effect */}
                <div className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none"
                     style={{ backgroundColor: 'var(--color-primary-600)' }} />
              </div>
            </Link>
          )
        })}
      </div>
    </div>
  )
}
