'use client'

import { useSession, signOut } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect, ReactNode } from 'react'
import { Sidebar } from './Sidebar'
import { BottomNav } from './BottomNav'
import { Button } from '@/components/ui/button'

interface AppLayoutProps {
  children: ReactNode
}

export function AppLayout({ children }: AppLayoutProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  // Temporarily bypass authentication for development
  const isDevelopment = process.env.NODE_ENV === 'development'

  useEffect(() => {
    if (status === 'loading') return
    if (!session && !isDevelopment) router.push('/auth/signin')
  }, [session, status, router, isDevelopment])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-4xl mb-4">💰</div>
          <div className="text-lg font-medium text-gray-900">Loading BudgetTrack...</div>
          <div className="text-sm text-gray-500 mt-2">Please wait while we prepare your dashboard</div>
        </div>
      </div>
    )
  }

  if (!session && !isDevelopment) {
    return null
  }

  // Create a mock session for development
  const effectiveSession = session || (isDevelopment ? {
    user: {
      id: 'dev_user_123',
      name: 'Development User',
      email: '<EMAIL>'
    }
  } : null)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Desktop Layout */}
      <div className="hidden md:flex h-screen">
        {/* Sidebar */}
        <Sidebar />
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 🎨 Enhanced Header - World-Class Design */}
          <header className="bg-white border-b px-6 py-4 backdrop-blur-sm"
                  style={{
                    borderColor: 'var(--color-gray-100)',
                    boxShadow: 'var(--shadow-xs)'
                  }}>
            <div className="flex items-center justify-between">
              {/* Left side - Enhanced Contextual Navigation */}
              <div className="flex items-center space-x-4">
                {/* Page Context with Enhanced Typography */}
                <div className="flex items-center space-x-3">
                  <h1 className="text-heading-md font-semibold"
                      style={{ color: 'var(--color-gray-800)' }}>
                    {getPageTitle(pathname)}
                  </h1>
                  {/* Breadcrumb indicator */}
                  {getPageTitle(pathname) !== 'Dashboard' && (
                    <div className="flex items-center space-x-2">
                      <div className="w-1 h-1 rounded-full"
                           style={{ backgroundColor: 'var(--color-gray-400)' }}></div>
                      <span className="text-label-md"
                            style={{ color: 'var(--color-gray-500)' }}>
                        Active
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Center/Right side - Period Selection and User Profile */}
              <div className="flex items-center space-x-6">
                {/* Period Selection - Only show on Dashboard */}
                {pathname === '/dashboard' && (
                  <div className="flex items-center space-x-2">
                    <Button variant="default" size="sm" className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1">
                      This month
                    </Button>
                    <Button variant="outline" size="sm" className="text-gray-600 border-gray-300 text-xs px-3 py-1">
                      Last month
                    </Button>
                    <Button variant="outline" size="sm" className="text-gray-600 border-gray-300 flex items-center space-x-1 text-xs px-3 py-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                        <line x1="16" y1="2" x2="16" y2="6"/>
                        <line x1="8" y1="2" x2="8" y2="6"/>
                        <line x1="3" y1="10" x2="21" y2="10"/>
                      </svg>
                      <span>Select period</span>
                    </Button>
                  </div>
                )}

                {/* Right side - Search, Notification, User Profile */}
                <div className="flex items-center space-x-3">
                  {/* Search Icon */}
                  <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700 p-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="11" cy="11" r="8"/>
                      <path d="m21 21-4.35-4.35"/>
                    </svg>
                  </Button>

                  {/* Notification Icon */}
                  <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700 p-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                      <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
                    </svg>
                  </Button>

                  {/* 👤 Enhanced User Profile */}
                  <div className="flex items-center space-x-3 p-2 rounded-xl transition-all duration-300 hover:shadow-sm cursor-pointer"
                       style={{ backgroundColor: 'transparent' }}
                       onMouseEnter={(e) => {
                         e.currentTarget.style.backgroundColor = 'var(--color-gray-50)'
                       }}
                       onMouseLeave={(e) => {
                         e.currentTarget.style.backgroundColor = 'transparent'
                       }}>
                    {/* Enhanced Avatar */}
                    <div className="relative">
                      <div className="w-9 h-9 rounded-full flex items-center justify-center text-white text-sm font-semibold shadow-sm"
                           style={{
                             background: 'linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%)'
                           }}>
                        {effectiveSession?.user?.name?.charAt(0) || 'A'}
                      </div>
                      {/* Online indicator */}
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white pointer-events-none"
                           style={{ backgroundColor: 'var(--color-success-400)' }}></div>
                    </div>

                    {/* User Info */}
                    <div className="flex items-center space-x-2">
                      <div className="text-left">
                        <span className="text-body-sm font-semibold block"
                              style={{ color: 'var(--color-gray-700)' }}>
                          {effectiveSession?.user?.name?.split(' ')[0] || 'Anthony'}
                        </span>
                        <span className="text-label-sm"
                              style={{ color: 'var(--color-gray-500)' }}>
                          Premium
                        </span>
                      </div>
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
                           style={{ color: 'var(--color-gray-400)' }}
                           className="transition-transform duration-200 hover:rotate-180">
                        <path d="m6 9 6 6 6-6"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </header>
          
          {/* Page Content */}
          <main className="flex-1 overflow-auto p-6">
            {children}
          </main>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden min-h-screen pb-20">
        {/* Mobile Header */}
        <header className="bg-white border-b border-gray-200 px-4 py-4 sticky top-0 z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div>
                <h1 className="text-lg font-semibold text-slate-800">{getPageTitle(pathname)}</h1>
                <p className="text-xs text-gray-500">Hi, {effectiveSession?.user?.name?.split(' ')[0]}!</p>
              </div>
            </div>
            <Button
              onClick={() => signOut({ callbackUrl: '/auth/signin' })}
              variant="ghost"
              size="sm"
              className="text-teal-600"
            >
              Sign Out
            </Button>
          </div>
        </header>

        {/* Mobile Content */}
        <main className="p-4">
          {children}
        </main>

        {/* Bottom Navigation */}
        <BottomNav />
      </div>
    </div>
  )
}

function getPageTitle(pathname: string): string {
  const titles: Record<string, string> = {
    '/dashboard': 'Dashboard',
    '/budget': 'Budgets',
    '/budget/create': 'Create Budget',
    '/income': 'Income',
    '/expenses': 'Expenses',
    '/analytics': 'Analytics',
    '/categories': 'Categories',
    '/goals': 'Goals',
    '/notifications': 'Notifications',
    '/settings': 'Settings',
    '/help': 'Help'
  }

  // Handle dynamic routes
  if (pathname.startsWith('/budget/view/')) {
    return 'Budget Details'
  }

  return titles[pathname] || 'Dashboard'
}
