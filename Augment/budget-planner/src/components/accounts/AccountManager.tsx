'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Plus,
  Building2,
  CreditCard,
  Banknote,
  Smartphone,
  TrendingUp,
  PiggyBank,
  Home,
  FileText,
  MoreHorizontal,
  Eye,
  Upload,
  ArrowRightLeft,
  RefreshCw,
  Download,
  Edit,
  Trash2
} from 'lucide-react'
import { FinancialAccount, AccountType, ACCOUNT_TYPE_CONFIG, ACCOUNT_PROVIDER_CONFIG } from '@/types/account'

interface AccountManagerProps {
  accounts: FinancialAccount[]
  onAccountSelect: (account: FinancialAccount) => void
  onAddAccount: () => void
  onImportCSV: (accountId: string) => void
}

// Mock account data for demonstration
const mockAccounts: FinancialAccount[] = [
  {
    id: '1',
    userId: 'user1',
    name: 'Chase Freedom Checking',
    type: 'checking',
    provider: 'chase',
    accountNumber: '****1234',
    balance: 3247.82,
    availableBalance: 3247.82,
    isActive: true,
    isPrimary: true,
    color: '#0066B2',
    icon: 'building-2',
    currency: 'USD',
    description: 'Primary checking account',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: '2',
    userId: 'user1',
    name: 'Chase Sapphire Preferred',
    type: 'credit_card',
    provider: 'chase',
    accountNumber: '****5678',
    balance: -1247.50,
    availableBalance: 8752.50,
    creditLimit: 10000,
    isActive: true,
    isPrimary: false,
    color: '#0066B2',
    icon: 'credit-card',
    currency: 'USD',
    description: 'Travel rewards credit card',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: '3',
    userId: 'user1',
    name: 'Wells Fargo Savings',
    type: 'savings',
    provider: 'wells_fargo',
    accountNumber: '****9012',
    balance: 15420.00,
    availableBalance: 15420.00,
    isActive: true,
    isPrimary: true,
    color: '#D71921',
    icon: 'piggy-bank',
    currency: 'USD',
    description: 'Emergency fund savings',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: '4',
    userId: 'user1',
    name: 'Cash Wallet',
    type: 'cash',
    provider: 'other',
    balance: 127.00,
    isActive: true,
    isPrimary: true,
    color: '#F59E0B',
    icon: 'banknote',
    currency: 'USD',
    description: 'Physical cash on hand',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  }
]

export function AccountManager({ 
  accounts = mockAccounts, 
  onAccountSelect, 
  onAddAccount, 
  onImportCSV 
}: AccountManagerProps) {
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(null)

  const getAccountIcon = (type: AccountType) => {
    const iconMap = {
      checking: Building2,
      savings: PiggyBank,
      credit_card: CreditCard,
      cash: Banknote,
      investment: TrendingUp,
      digital_wallet: Smartphone,
      loan: FileText,
      mortgage: Home
    }
    return iconMap[type] || Building2
  }

  const formatBalance = (account: FinancialAccount) => {
    const balance = account.balance || 0
    const isNegative = balance < 0
    const absBalance = Math.abs(balance)
    
    if (account.type === 'credit_card') {
      return {
        amount: `$${absBalance.toLocaleString()}`,
        label: isNegative ? 'Balance Owed' : 'Credit Available',
        color: isNegative ? 'text-red-600' : 'text-green-600'
      }
    }
    
    return {
      amount: `$${absBalance.toLocaleString()}`,
      label: 'Available Balance',
      color: isNegative ? 'text-red-600' : 'text-gray-900'
    }
  }

  const getAccountActions = (account: FinancialAccount) => {
    const actions = []
    
    if (['checking', 'savings', 'credit_card'].includes(account.type)) {
      actions.push({
        icon: Upload,
        label: 'Import CSV',
        action: () => onImportCSV(account.id),
        color: 'text-blue-600'
      })
    }
    
    if (['checking', 'savings'].includes(account.type)) {
      actions.push({
        icon: ArrowRightLeft,
        label: 'Transfer',
        action: () => console.log('Transfer from', account.id),
        color: 'text-purple-600'
      })
    }
    
    actions.push({
      icon: RefreshCw,
      label: 'Update Balance',
      action: () => console.log('Update balance', account.id),
      color: 'text-green-600'
    })
    
    actions.push({
      icon: Download,
      label: 'Export',
      action: () => console.log('Export', account.id),
      color: 'text-gray-600'
    })
    
    return actions
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Financial Accounts</h2>
          <p className="text-gray-600">Manage your bank accounts, credit cards, and cash</p>
        </div>
        <Button onClick={onAddAccount} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          Add Account
        </Button>
      </div>

      {/* Account Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {accounts.map((account) => {
          const IconComponent = getAccountIcon(account.type)
          const balanceInfo = formatBalance(account)
          const typeConfig = ACCOUNT_TYPE_CONFIG[account.type]
          const providerConfig = ACCOUNT_PROVIDER_CONFIG[account.provider]
          const actions = getAccountActions(account)

          return (
            <Card 
              key={account.id}
              className={`hover:shadow-lg transition-all duration-200 cursor-pointer ${
                selectedAccountId === account.id ? 'ring-2 ring-blue-500 border-blue-200' : ''
              }`}
              onClick={() => {
                setSelectedAccountId(account.id)
                onAccountSelect(account)
              }}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-12 h-12 rounded-xl flex items-center justify-center text-white"
                      style={{ backgroundColor: account.color }}
                    >
                      <IconComponent className="w-6 h-6" />
                    </div>
                    <div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        {account.name}
                      </CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {typeConfig.label}
                        </Badge>
                        {account.isPrimary && (
                          <Badge className="text-xs bg-blue-100 text-blue-700">
                            Primary
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  {/* Balance */}
                  <div>
                    <div className={`text-2xl font-bold ${balanceInfo.color}`}>
                      {balanceInfo.amount}
                    </div>
                    <div className="text-sm text-gray-500">{balanceInfo.label}</div>
                    {account.accountNumber && (
                      <div className="text-xs text-gray-400 mt-1">
                        {account.accountNumber}
                      </div>
                    )}
                  </div>

                  {/* Credit Card Specific Info */}
                  {account.type === 'credit_card' && account.creditLimit && (
                    <div className="text-sm text-gray-600">
                      <div className="flex justify-between">
                        <span>Credit Limit:</span>
                        <span>${account.creditLimit.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Available:</span>
                        <span className="text-green-600">
                          ${(account.creditLimit + (account.balance || 0)).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Quick Actions */}
                  <div className="flex flex-wrap gap-2">
                    {actions.slice(0, 2).map((action, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          action.action()
                        }}
                        className="flex items-center space-x-1 text-xs"
                      >
                        <action.icon className={`w-3 h-3 ${action.color}`} />
                        <span>{action.label}</span>
                      </Button>
                    ))}
                  </div>

                  {/* Provider Info */}
                  <div className="pt-3 border-t border-gray-100">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{providerConfig.label}</span>
                      <span>
                        Last updated: {account.updatedAt.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {accounts.filter(a => a.type === 'checking' || a.type === 'savings').length}
            </div>
            <div className="text-sm text-gray-600">Bank Accounts</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {accounts.filter(a => a.type === 'credit_card').length}
            </div>
            <div className="text-sm text-gray-600">Credit Cards</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              ${accounts
                .filter(a => a.type === 'checking' || a.type === 'savings')
                .reduce((sum, a) => sum + (a.balance || 0), 0)
                .toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Total Cash</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              ${accounts
                .filter(a => a.type === 'credit_card')
                .reduce((sum, a) => sum + Math.abs(a.balance || 0), 0)
                .toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Credit Used</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
