'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Filter,
  Calendar,
  Tag,
  X,
  TrendingUp,
  TrendingDown,
  DollarSign
} from 'lucide-react'

interface ExpenseFiltersProps {
  filters: {
    search: string
    category: string
    dateRange: string
    type: string
    account: string
  }
  onFiltersChange: (filters: any) => void
}

const CATEGORIES = [
  'Housing',
  'Food & Dining',
  'Transportation',
  'Utilities',
  'Healthcare',
  'Entertainment',
  'Shopping',
  'Savings'
]

const ACCOUNTS = [
  { id: '1', name: 'Chase Freedom Checking', type: 'checking' },
  { id: '2', name: 'Chase Sapphire Preferred', type: 'credit_card' },
  { id: '3', name: 'Wells Fargo Savings', type: 'savings' },
  { id: '4', name: 'Cash Wallet', type: 'cash' }
]

const DATE_RANGES = [
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' },
  { value: 'year', label: 'This Year' },
  { value: 'custom', label: 'Custom Range' }
]

export function ExpenseFilters({ filters, onFiltersChange }: ExpenseFiltersProps) {
  const updateFilter = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      category: '',
      dateRange: '',
      type: '',
      account: ''
    })
  }

  const hasActiveFilters = Object.values(filters).some(value => value !== '')

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Search and Quick Filters Row */}
          <div className="flex flex-col md:flex-row md:items-center md:space-x-4 space-y-4 md:space-y-0">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search transactions..."
                value={filters.search}
                onChange={(e) => updateFilter('search', e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Type Filter */}
            <div className="flex space-x-2">
              <Button
                variant={filters.type === '' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilter('type', '')}
                className="flex items-center space-x-1"
              >
                <span>All</span>
              </Button>
              <Button
                variant={filters.type === 'expense' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilter('type', 'expense')}
                className="flex items-center space-x-1"
              >
                <TrendingDown className="w-3 h-3" />
                <span>Expenses</span>
              </Button>
              <Button
                variant={filters.type === 'income' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilter('type', 'income')}
                className="flex items-center space-x-1"
              >
                <TrendingUp className="w-3 h-3" />
                <span>Income</span>
              </Button>
            </div>

            {/* Clear Filters */}
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="flex items-center space-x-1 text-gray-600"
              >
                <X className="w-3 h-3" />
                <span>Clear</span>
              </Button>
            )}
          </div>

          {/* Category Filter */}
          <div>
            <div className="flex items-center space-x-2 mb-3">
              <Tag className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Categories</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant={filters.category === '' ? 'default' : 'outline'}
                className="cursor-pointer hover:bg-gray-100"
                onClick={() => updateFilter('category', '')}
              >
                All Categories
              </Badge>
              {CATEGORIES.map((category) => (
                <Badge
                  key={category}
                  variant={filters.category === category ? 'default' : 'outline'}
                  className="cursor-pointer hover:bg-gray-100"
                  onClick={() => updateFilter('category', category)}
                >
                  {category}
                </Badge>
              ))}
            </div>
          </div>

          {/* Account Filter */}
          <div>
            <div className="flex items-center space-x-2 mb-3">
              <DollarSign className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Accounts</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant={filters.account === '' ? 'default' : 'outline'}
                className="cursor-pointer hover:bg-gray-100"
                onClick={() => updateFilter('account', '')}
              >
                All Accounts
              </Badge>
              {ACCOUNTS.map((account) => (
                <Badge
                  key={account.id}
                  variant={filters.account === account.id ? 'default' : 'outline'}
                  className="cursor-pointer hover:bg-gray-100"
                  onClick={() => updateFilter('account', account.id)}
                >
                  {account.name}
                </Badge>
              ))}
            </div>
          </div>

          {/* Date Range Filter */}
          <div>
            <div className="flex items-center space-x-2 mb-3">
              <Calendar className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Date Range</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant={filters.dateRange === '' ? 'default' : 'outline'}
                className="cursor-pointer hover:bg-gray-100"
                onClick={() => updateFilter('dateRange', '')}
              >
                All Time
              </Badge>
              {DATE_RANGES.map((range) => (
                <Badge
                  key={range.value}
                  variant={filters.dateRange === range.value ? 'default' : 'outline'}
                  className="cursor-pointer hover:bg-gray-100"
                  onClick={() => updateFilter('dateRange', range.value)}
                >
                  {range.label}
                </Badge>
              ))}
            </div>
          </div>

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <div className="pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Filter className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Active Filters:</span>
                  <div className="flex items-center space-x-2">
                    {filters.search && (
                      <Badge variant="secondary" className="flex items-center space-x-1">
                        <span>Search: "{filters.search}"</span>
                        <button
                          onClick={() => updateFilter('search', '')}
                          className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    )}
                    {filters.category && (
                      <Badge variant="secondary" className="flex items-center space-x-1">
                        <span>Category: {filters.category}</span>
                        <button
                          onClick={() => updateFilter('category', '')}
                          className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    )}
                    {filters.type && (
                      <Badge variant="secondary" className="flex items-center space-x-1">
                        <span>Type: {filters.type}</span>
                        <button
                          onClick={() => updateFilter('type', '')}
                          className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    )}
                    {filters.dateRange && (
                      <Badge variant="secondary" className="flex items-center space-x-1">
                        <span>Date: {DATE_RANGES.find(r => r.value === filters.dateRange)?.label}</span>
                        <button
                          onClick={() => updateFilter('dateRange', '')}
                          className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    )}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-gray-600"
                >
                  Clear All
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
