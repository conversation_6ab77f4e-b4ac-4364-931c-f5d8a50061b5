'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardH<PERSON>er, Card<PERSON>itle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Eye,
  Edit,
  MoreHorizontal,
  Calendar,
  DollarSign,
  Tag,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  AlertCircle,
  Filter
} from 'lucide-react'

interface Transaction {
  id: string
  accountId: string
  date: Date
  description: string
  amount: number
  type: 'expense' | 'income'
  category: {
    name: string
    type: 'NEED' | 'WANT' | 'SMILE'
    icon: string
    color: string
  }
  subcategory?: string
  confidence: number
  merchantName?: string
  isReviewed: boolean
}

interface TransactionListProps {
  transactions: Transaction[]
  filters: {
    search: string
    category: string
    dateRange: string
    type: string
    account: string
  }
  onTransactionUpdate: (id: string, updates: Partial<Transaction>) => void
}

export function TransactionList({ transactions, filters, onTransactionUpdate }: TransactionListProps) {
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([])
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'category'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // Mock account data for display
  const getAccountName = (accountId: string) => {
    const accountMap: { [key: string]: string } = {
      '1': 'Chase Freedom Checking',
      '2': 'Chase Sapphire Preferred',
      '3': 'Wells Fargo Savings',
      '4': 'Cash Wallet'
    }
    return accountMap[accountId] || `Account ${accountId}`
  }

  // Filter transactions
  const filteredTransactions = transactions.filter(transaction => {
    if (filters.search && !transaction.description.toLowerCase().includes(filters.search.toLowerCase())) {
      return false
    }
    if (filters.category && transaction.category.name !== filters.category) {
      return false
    }
    if (filters.type && transaction.type !== filters.type) {
      return false
    }
    if (filters.account && transaction.accountId !== filters.account) {
      return false
    }
    return true
  })

  // Sort transactions
  const sortedTransactions = [...filteredTransactions].sort((a, b) => {
    let comparison = 0
    
    switch (sortBy) {
      case 'date':
        comparison = a.date.getTime() - b.date.getTime()
        break
      case 'amount':
        comparison = a.amount - b.amount
        break
      case 'category':
        comparison = a.category.name.localeCompare(b.category.name)
        break
    }
    
    return sortOrder === 'asc' ? comparison : -comparison
  })

  const getCategoryIcon = (iconName: string) => {
    const iconMap: { [key: string]: any } = {
      'home': '🏠',
      'utensils': '🍽️',
      'shopping-cart': '🛒',
      'car': '🚗',
      'heart': '❤️',
      'zap': '⚡',
      'film': '🎬',
      'shopping-bag': '🛍️'
    }
    return iconMap[iconName] || '📝'
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50 border-green-200'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getConfidenceText = (confidence: number) => {
    if (confidence >= 0.8) return 'High'
    if (confidence >= 0.6) return 'Medium'
    return 'Low'
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="w-5 h-5" />
            <span>Transactions ({sortedTransactions.length})</span>
          </CardTitle>
          
          {/* Sort Controls */}
          <div className="flex items-center space-x-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="text-sm border border-gray-300 rounded-lg px-3 py-1"
            >
              <option value="date">Sort by Date</option>
              <option value="amount">Sort by Amount</option>
              <option value="category">Sort by Category</option>
            </select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'asc' ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {sortedTransactions.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📊</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Transactions Found</h3>
            <p className="text-gray-600">
              {transactions.length === 0 
                ? 'Import your bank CSV to get started with expense tracking'
                : 'Try adjusting your filters to see more transactions'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {sortedTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-xl hover:border-gray-300 transition-colors"
              >
                {/* Transaction Info */}
                <div className="flex items-center space-x-4 flex-1">
                  {/* Category Icon */}
                  <div 
                    className="w-12 h-12 rounded-xl flex items-center justify-center text-white font-semibold"
                    style={{ backgroundColor: transaction.category.color }}
                  >
                    {getCategoryIcon(transaction.category.icon)}
                  </div>
                  
                  {/* Details */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-1">
                      <h4 className="font-semibold text-gray-900">
                        {transaction.merchantName || transaction.description}
                      </h4>
                      <Badge 
                        variant="outline"
                        className={`text-xs ${
                          transaction.category.type === 'NEED' ? 'border-blue-200 text-blue-700 bg-blue-50' :
                          transaction.category.type === 'WANT' ? 'border-orange-200 text-orange-700 bg-orange-50' :
                          'border-green-200 text-green-700 bg-green-50'
                        }`}
                      >
                        {transaction.category.name}
                      </Badge>
                      {transaction.subcategory && (
                        <Badge variant="secondary" className="text-xs">
                          {transaction.subcategory}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span className="flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {transaction.date.toLocaleDateString()}
                      </span>
                      <span className="flex items-center">
                        <DollarSign className="w-3 h-3 mr-1" />
                        {getAccountName(transaction.accountId)}
                      </span>
                      {transaction.merchantName && transaction.merchantName !== transaction.description && (
                        <span className="text-xs text-gray-500">
                          {transaction.description}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Amount and Actions */}
                <div className="flex items-center space-x-4">
                  {/* Confidence Badge */}
                  <Badge 
                    variant="outline"
                    className={`text-xs ${getConfidenceColor(transaction.confidence)}`}
                  >
                    {transaction.isReviewed ? (
                      <CheckCircle className="w-3 h-3 mr-1" />
                    ) : (
                      <AlertCircle className="w-3 h-3 mr-1" />
                    )}
                    {getConfidenceText(transaction.confidence)}
                  </Badge>
                  
                  {/* Amount */}
                  <div className="text-right">
                    <div className={`text-lg font-bold ${
                      transaction.type === 'expense' ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {transaction.type === 'expense' ? '-' : '+'}${transaction.amount.toFixed(2)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {transaction.type === 'expense' ? 'Expense' : 'Income'}
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        // Handle edit transaction
                        console.log('Edit transaction:', transaction.id)
                      }}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        // Handle view details
                        console.log('View transaction:', transaction.id)
                      }}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination would go here */}
        {sortedTransactions.length > 0 && (
          <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
            <div className="text-sm text-gray-600">
              Showing {sortedTransactions.length} of {transactions.length} transactions
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" disabled>
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
