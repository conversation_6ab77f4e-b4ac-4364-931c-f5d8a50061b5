'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  X,
  Download,
  Eye,
  Zap
} from 'lucide-react'
// import { CSVImporter } from '@/lib/csv-import'
// import { SUPPORTED_BANK_FORMATS } from '@/types/transaction'

// Temporary mock data for demonstration
const SUPPORTED_BANK_FORMATS = [
  { id: 'chase', name: 'Chase Bank' },
  { id: 'bank_of_america', name: 'Bank of America' },
  { id: 'wells_fargo', name: 'Wells Fargo' },
  { id: 'generic', name: 'Generic CSV' }
]

interface CSVImportModalProps {
  isOpen: boolean
  onClose: () => void
  onImportComplete: (transactions: any[]) => void
  selectedAccountId?: string
  accounts?: Array<{
    id: string
    name: string
    type: string
    provider: string
    accountNumber?: string
  }>
}

// Mock accounts for demonstration
const mockAccounts = [
  { id: '1', name: 'Chase Freedom Checking', type: 'checking', provider: 'chase', accountNumber: '****1234' },
  { id: '2', name: 'Chase Sapphire Preferred', type: 'credit_card', provider: 'chase', accountNumber: '****5678' },
  { id: '3', name: 'Wells Fargo Savings', type: 'savings', provider: 'wells_fargo', accountNumber: '****9012' },
  { id: '4', name: 'Cash Wallet', type: 'cash', provider: 'other' }
]

export function CSVImportModal({
  isOpen,
  onClose,
  onImportComplete,
  selectedAccountId,
  accounts = mockAccounts
}: CSVImportModalProps) {
  const [step, setStep] = useState<'upload' | 'preview' | 'processing' | 'complete'>('upload')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [bankFormat, setBankFormat] = useState('generic')
  const [selectedAccount, setSelectedAccount] = useState(selectedAccountId || accounts[0]?.id || '')
  const [rawTransactions, setRawTransactions] = useState<any[]>([])
  const [processedTransactions, setProcessedTransactions] = useState<any[]>([])
  const [importStats, setImportStats] = useState<any>(null)
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        setError('Please select a CSV file')
        return
      }
      setSelectedFile(file)
      setError(null)
    }
  }, [])

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file && (file.type === 'text/csv' || file.name.endsWith('.csv'))) {
      setSelectedFile(file)
      setError(null)
    } else {
      setError('Please drop a CSV file')
    }
  }, [])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  const processFile = async () => {
    if (!selectedFile) return

    setStep('processing')
    setProgress(0)
    setError(null)

    try {
      // Simulate processing for demo
      setProgress(25)
      await new Promise(resolve => setTimeout(resolve, 500))

      setProgress(50)
      await new Promise(resolve => setTimeout(resolve, 500))

      // Mock processed transactions
      const mockProcessed = [
        {
          id: '1',
          date: new Date('2024-01-15'),
          description: 'STARBUCKS STORE #1234',
          amount: 5.47,
          type: 'expense',
          category: { name: 'Food & Dining', type: 'WANT', icon: 'utensils', color: '#F59E0B' },
          subcategory: 'Coffee',
          confidence: 0.95,
          merchantName: 'Starbucks',
          isReviewed: true
        },
        {
          id: '2',
          date: new Date('2024-01-14'),
          description: 'WALMART SUPERCENTER',
          amount: 127.83,
          type: 'expense',
          category: { name: 'Groceries', type: 'NEED', icon: 'shopping-cart', color: '#10B981' },
          subcategory: 'Groceries',
          confidence: 0.88,
          merchantName: 'Walmart',
          isReviewed: true
        }
      ]

      setProcessedTransactions(mockProcessed)

      setProgress(75)
      const stats = generateImportStats(mockProcessed)
      setImportStats(stats)

      setProgress(100)
      setStep('preview')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Import failed')
      setStep('upload')
    }
  }

  const confirmImport = () => {
    onImportComplete(processedTransactions)
    setStep('complete')
  }

  const resetModal = () => {
    setStep('upload')
    setSelectedFile(null)
    setRawTransactions([])
    setProcessedTransactions([])
    setImportStats(null)
    setProgress(0)
    setError(null)
  }

  const generateImportStats = (transactions: any[]) => {
    const expenses = transactions.filter(t => t.type === 'expense')
    const income = transactions.filter(t => t.type === 'income')
    const categorized = transactions.filter(t => t.confidence > 0.7)
    
    return {
      total: transactions.length,
      expenses: expenses.length,
      income: income.length,
      categorized: categorized.length,
      uncategorized: transactions.length - categorized.length,
      totalAmount: expenses.reduce((sum, t) => sum + t.amount, 0),
      dateRange: {
        start: new Date(Math.min(...transactions.map(t => t.date.getTime()))),
        end: new Date(Math.max(...transactions.map(t => t.date.getTime())))
      }
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
              <Upload className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Import Bank Transactions</h2>
              <p className="text-gray-600">Upload your CSV file to automatically categorize expenses</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'upload' && (
            <div className="space-y-6">
              {/* Account Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Select Account to Import To
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {accounts.map((account) => (
                    <button
                      key={account.id}
                      onClick={() => setSelectedAccount(account.id)}
                      className={`p-4 rounded-xl border-2 text-left transition-all ${
                        selectedAccount === account.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-white text-sm font-semibold ${
                          account.type === 'checking' ? 'bg-blue-500' :
                          account.type === 'savings' ? 'bg-green-500' :
                          account.type === 'credit_card' ? 'bg-red-500' :
                          'bg-orange-500'
                        }`}>
                          {account.type === 'checking' ? '🏦' :
                           account.type === 'savings' ? '🐷' :
                           account.type === 'credit_card' ? '💳' : '💰'}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{account.name}</div>
                          <div className="text-sm text-gray-500">
                            {account.accountNumber || account.type}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Bank Format Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Select Your Bank Format
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {SUPPORTED_BANK_FORMATS.map((format) => (
                    <button
                      key={format.id}
                      onClick={() => setBankFormat(format.id)}
                      className={`p-3 rounded-xl border-2 text-sm font-medium transition-all ${
                        bankFormat === format.id
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {format.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* File Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Upload CSV File
                </label>
                <div
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors"
                >
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-lg font-medium text-gray-900 mb-2">
                    Drop your CSV file here, or click to browse
                  </p>
                  <p className="text-sm text-gray-500 mb-4">
                    Supports bank statements from major banks
                  </p>
                  <input
                    type="file"
                    accept=".csv"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="csv-upload"
                  />
                  <label
                    htmlFor="csv-upload"
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Choose File
                  </label>
                </div>

                {selectedFile && (
                  <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                      <span className="text-sm font-medium text-green-800">
                        {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                      </span>
                    </div>
                  </div>
                )}

                {error && (
                  <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center">
                      <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                      <span className="text-sm font-medium text-red-800">{error}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  onClick={processFile}
                  disabled={!selectedFile}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Process File
                </Button>
              </div>
            </div>
          )}

          {step === 'processing' && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Zap className="w-8 h-8 text-blue-600 animate-pulse" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Processing Transactions</h3>
              <p className="text-gray-600 mb-6">Parsing CSV and categorizing expenses...</p>
              <div className="max-w-md mx-auto">
                <Progress value={progress} className="h-3" />
                <p className="text-sm text-gray-500 mt-2">{progress}% complete</p>
              </div>
            </div>
          )}

          {step === 'preview' && importStats && (
            <div className="space-y-6">
              {/* Import Summary */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{importStats.total}</div>
                    <div className="text-sm text-gray-600">Total Transactions</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-red-600">{importStats.expenses}</div>
                    <div className="text-sm text-gray-600">Expenses</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{importStats.categorized}</div>
                    <div className="text-sm text-gray-600">Auto-Categorized</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-orange-600">{importStats.uncategorized}</div>
                    <div className="text-sm text-gray-600">Need Review</div>
                  </CardContent>
                </Card>
              </div>

              {/* Sample Transactions */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Preview (First 5 Transactions)</h4>
                <div className="space-y-2">
                  {processedTransactions.slice(0, 5).map((transaction, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="text-sm font-medium text-gray-900">
                          {transaction.description}
                        </div>
                        <Badge variant={transaction.confidence > 0.7 ? 'default' : 'secondary'}>
                          {transaction.category.name}
                        </Badge>
                      </div>
                      <div className="text-sm font-semibold text-gray-900">
                        ${transaction.amount.toFixed(2)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3">
                <Button variant="outline" onClick={resetModal}>
                  Start Over
                </Button>
                <Button onClick={confirmImport} className="bg-green-600 hover:bg-green-700">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Import {importStats.total} Transactions
                </Button>
              </div>
            </div>
          )}

          {step === 'complete' && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Import Complete!</h3>
              <p className="text-gray-600 mb-6">
                Successfully imported {importStats?.total} transactions
              </p>
              <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700">
                View Transactions
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
