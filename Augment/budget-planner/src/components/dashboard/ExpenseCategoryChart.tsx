'use client'

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { useState, useEffect } from 'react'

interface CategoryData {
  name: string
  value: number
  lastMonthValue: number
  color: string
  iconType?: 'home' | 'shopping-cart' | 'car' | 'zap' | 'heart' | 'shield' | 'film' | 'shopping-bag' | 'graduation-cap' | 'smartphone'
  trend?: 'up' | 'down' | 'stable'
  trendPercentage?: number
}

interface ExpenseCategoryChartProps {
  totalAmount: number
  lastMonthTotal: number
  data: CategoryData[]
  currency?: string
}

// 🎨 World-Class Chart Icon - Apple/Google Inspired
function ChartIcon() {
  return (
    <div className="relative w-12 h-12 rounded-2xl flex items-center justify-center ring-1 ring-white/20"
         style={{
           background: 'linear-gradient(135deg, var(--color-wants-500) 0%, var(--color-wants-600) 100%)',
           boxShadow: 'var(--shadow-lg), 0 0 20px var(--color-wants-500)'
         }}>
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
      {/* Heroicon: chart-bar */}
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" className="relative z-10">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
      </svg>
      <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse"
           style={{ backgroundColor: 'var(--color-wants-400)' }}></div>
    </div>
  )
}

// 💼 Professional Heroicons - Button Style Design
function CategoryIcon({ type, className = "" }: { type: string, className?: string }) {
  const iconComponents = {
    'home': (
      // Heroicon: home
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
      </svg>
    ),
    'shopping-cart': (
      // Heroicon: shopping-cart
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
      </svg>
    ),
    'car': (
      // Heroicon: truck
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 18.75a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 01-1.125-1.125V14.25m15.75 4.5a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h1.125A1.125 1.125 0 0021 17.25v-6.75m-15.75 0V8.25A2.25 2.25 0 017.5 6h1.5m0 0V5.25A2.25 2.25 0 0111.25 3h1.5a2.25 2.25 0 012.25 2.25V6h1.5a2.25 2.25 0 012.25 2.25v6.75" />
      </svg>
    ),
    'zap': (
      // Heroicon: bolt
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
      </svg>
    ),
    'heart': (
      // Heroicon: heart
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
      </svg>
    ),
    'shield': (
      // Heroicon: shield-check
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
      </svg>
    ),
    'film': (
      // Heroicon: film
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 01-1.125-1.125M3.375 19.5h7.5c.621 0 1.125-.504 1.125-1.125m-9.75 0V5.625m0 0A2.25 2.25 0 015.625 3.375h4.5c.621 0 1.125.504 1.125 1.125v2.25m-9.75 0V8.25m0 0V12m9.75-3.75h3.375c.621 0 1.125.504 1.125 1.125v2.25m0 0V12m0 0v6.75m0 0V15m0 0h3.375c.621 0 1.125.504 1.125 1.125v2.25M12 10.5V8.25m0 0V5.625M12 8.25h.008v.008H12V8.25z" />
      </svg>
    ),
    'shopping-bag': (
      // Heroicon: shopping-bag
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
      </svg>
    ),
    'graduation-cap': (
      // Heroicon: academic-cap
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443m-7.007 11.55A5.981 5.981 0 006.75 15.75v-1.5" />
      </svg>
    ),
    'smartphone': (
      // Heroicon: device-phone-mobile
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 006 3.75v16.5a2.25 2.25 0 002.25 2.25h7.5A2.25 2.25 0 0018 20.25V3.75a2.25 2.25 0 00-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
      </svg>
    )
  }

  return (
    <div className={`w-10 h-10 rounded-2xl flex items-center justify-center border transition-all duration-300 ${className}`}
         style={{
           background: 'linear-gradient(135deg, var(--color-wants-500) 0%, var(--color-wants-600) 100%)',
           borderColor: 'var(--color-wants-600)',
           color: 'white',
           boxShadow: 'none'
         }}>
      {iconComponents[type as keyof typeof iconComponents] || iconComponents['home']}
    </div>
  )
}

// 🎨 Professional Color Palette - Stripe/Linear Inspired
const CATEGORY_COLORS = {
  'housing': '#6366F1',      // Indigo - Stability
  'food': '#10B981',         // Emerald - Essential
  'transport': '#F59E0B',    // Amber - Movement
  'utilities': '#3B82F6',    // Blue - Infrastructure
  'healthcare': '#EF4444',   // Red - Health
  'entertainment': '#8B5CF6', // Purple - Lifestyle
  'shopping': '#F97316',     // Orange - Retail
  'education': '#059669',    // Green - Growth
  'insurance': '#6B7280',    // Gray - Security
  'other': '#9CA3AF'         // Light Gray - Miscellaneous
}

const defaultData: CategoryData[] = [
  { name: 'Housing', value: 2800, lastMonthValue: 2800, color: CATEGORY_COLORS.housing, iconType: 'home', trend: 'stable', trendPercentage: 0 },
  { name: 'Food & Dining', value: 1200, lastMonthValue: 1150, color: CATEGORY_COLORS.food, iconType: 'shopping-cart', trend: 'up', trendPercentage: 4.3 },
  { name: 'Transportation', value: 950, lastMonthValue: 880, color: CATEGORY_COLORS.transport, iconType: 'car', trend: 'up', trendPercentage: 8.0 },
  { name: 'Utilities', value: 650, lastMonthValue: 680, color: CATEGORY_COLORS.utilities, iconType: 'zap', trend: 'down', trendPercentage: -4.4 },
  { name: 'Healthcare', value: 580, lastMonthValue: 620, color: CATEGORY_COLORS.healthcare, iconType: 'heart', trend: 'down', trendPercentage: -6.5 },
  { name: 'Entertainment', value: 450, lastMonthValue: 380, color: CATEGORY_COLORS.entertainment, iconType: 'film', trend: 'up', trendPercentage: 18.4 },
  { name: 'Shopping', value: 380, lastMonthValue: 320, color: CATEGORY_COLORS.shopping, iconType: 'shopping-bag', trend: 'up', trendPercentage: 18.8 },
  { name: 'Education', value: 320, lastMonthValue: 320, color: CATEGORY_COLORS.education, iconType: 'graduation-cap', trend: 'stable', trendPercentage: 0 }
]

// 🎯 World-Class Horizontal Bar Component - Apple/Google/Stripe Inspired
function HorizontalBarChart({ data, totalAmount, currency }: { data: CategoryData[], totalAmount: number, currency: string }) {
  const [animatedWidths, setAnimatedWidths] = useState<number[]>([])

  useEffect(() => {
    // Staggered animation for bars
    const timer = setTimeout(() => {
      setAnimatedWidths(data.map(item => (item.value / totalAmount) * 100))
    }, 100)
    return () => clearTimeout(timer)
  }, [data, totalAmount])

  return (
    <div className="space-y-4">
      {data.map((category, index) => {
        const percentage = Math.round((category.value / totalAmount) * 100)
        const trendIcon = category.trend === 'up' ? '↗️' : category.trend === 'down' ? '↘️' : '→'
        const trendColor = category.trend === 'up' ? 'var(--color-error-600)' : category.trend === 'down' ? 'var(--color-success-600)' : 'var(--color-gray-500)'

        return (
          <div key={index} className="group relative">
            {/* Category Header */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                <CategoryIcon type={category.iconType || 'home'} />
                <div>
                  <div className="text-body-md font-semibold text-gray-900">
                    {category.name}
                  </div>
                  <div className="text-label-sm text-gray-500">
                    {currency}{category.value.toLocaleString()}
                  </div>
                </div>
              </div>

              {/* Trend Indicator */}
              <div className="flex items-center gap-2">
                <span className="text-heading-sm font-bold" style={{ color: trendColor }}>
                  {trendIcon} {category.trendPercentage !== undefined ? `${Math.abs(category.trendPercentage)}%` : ''}
                </span>
                <span className="text-label-md font-bold text-gray-900">
                  {percentage}%
                </span>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="relative h-3 bg-gray-100 rounded-full overflow-hidden">
              <div
                className="absolute top-0 left-0 h-full rounded-full transition-all duration-1000 ease-out"
                style={{
                  backgroundColor: category.color,
                  width: `${animatedWidths[index] || 0}%`,
                  animationDelay: `${index * 100}ms`,
                  boxShadow: `0 0 8px ${category.color}40`
                }}
              />

              {/* Hover Effect */}
              <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-200 rounded-full" />
            </div>
          </div>
        )
      })}
    </div>
  )
}

export function ExpenseCategoryChart({
  totalAmount = 8750,
  lastMonthTotal = 7820,
  data = defaultData,
  currency = '$'
}: ExpenseCategoryChartProps) {
  const topCategories = data.slice(0, 5) // Show top 5 categories
  const monthOverMonthChange = ((totalAmount - lastMonthTotal) / lastMonthTotal) * 100
  const isIncreasing = monthOverMonthChange > 0

  return (
    <Card className="bg-white/80 backdrop-blur-sm border rounded-3xl transition-all duration-500 group hover:bg-white/90"
          style={{
            borderColor: 'var(--color-gray-200)',
            boxShadow: 'var(--shadow-lg)',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.borderColor = 'var(--color-wants-200)'
            e.currentTarget.style.boxShadow = 'var(--shadow-xl)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.borderColor = 'var(--color-gray-200)'
            e.currentTarget.style.boxShadow = 'var(--shadow-lg)'
          }}>

      {/* 🎨 World-Class Header - Apple/Google Inspired */}
      <CardHeader style={{ padding: 'var(--padding-card-lg) var(--padding-card-lg) var(--space-6) var(--padding-card-lg)' }}>
        <div className="flex items-start justify-between">
          <div className="flex items-center" style={{ gap: 'var(--space-4)' }}>
            <ChartIcon />
            <div style={{ gap: 'var(--space-1)' }} className="space-y-1">
              <CardTitle className="text-heading-md font-semibold tracking-tight"
                         style={{ color: 'var(--color-gray-900)' }}>
                Expense Categories
              </CardTitle>
              <div className="text-label-md font-medium"
                   style={{ color: 'var(--color-gray-500)' }}>
                vs Last Month Analysis
              </div>
            </div>
          </div>

          {/* 📊 Summary Stats */}
          <div className="text-right">
            <div className="text-display-md font-bold tracking-tight"
                 style={{ color: 'var(--color-gray-900)' }}>
              {currency}{totalAmount.toLocaleString()}
            </div>
            <div className="flex items-center justify-end gap-1 mt-1">
              <span className={`text-label-md font-semibold ${isIncreasing ? 'text-red-600' : 'text-green-600'}`}>
                {isIncreasing ? '↗️' : '↘️'} {Math.abs(monthOverMonthChange).toFixed(1)}%
              </span>
              <span className="text-label-md text-gray-500">vs last month</span>
            </div>
          </div>
        </div>
      </CardHeader>
      {/* 🚀 World-Class Horizontal Bar Chart Content */}
      <CardContent style={{ padding: 'var(--padding-card-lg)' }}>
        <HorizontalBarChart
          data={topCategories}
          totalAmount={totalAmount}
          currency={currency}
        />

        {/* 📊 Summary Section - Stripe Inspired */}
        <div className="mt-8 pt-6 border-t border-gray-100">
          <div className="grid grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-display-sm font-bold text-gray-900">
                {topCategories.length}
              </div>
              <div className="text-label-md text-gray-500 mt-1">
                Top Categories
              </div>
            </div>

            <div className="text-center">
              <div className="text-display-sm font-bold text-gray-900">
                {currency}{(totalAmount - lastMonthTotal).toLocaleString()}
              </div>
              <div className="text-label-md text-gray-500 mt-1">
                vs Last Month
              </div>
            </div>

            <div className="text-center">
              <div className="text-display-sm font-bold text-gray-900">
                {topCategories.find(c => c.trend === 'up')?.name || 'Housing'}
              </div>
              <div className="text-label-md text-gray-500 mt-1">
                Highest Category
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
