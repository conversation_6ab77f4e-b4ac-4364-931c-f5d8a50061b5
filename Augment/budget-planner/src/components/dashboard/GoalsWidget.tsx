'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Car, Shield, Plane, GraduationCap, Target, Star } from 'lucide-react'

interface Goal {
  name: string
  current: number
  target: number
  percentage: number
  color: string
  iconType?: 'car' | 'shield' | 'plane' | 'graduation-cap' | 'target' | 'star'
  priority?: 'high' | 'medium' | 'low'
}

interface GoalsWidgetProps {
  totalGoalAmount: number
  goals: Goal[]
  currency?: string
}

const defaultGoals: Goal[] = [
  { name: 'Buy Car', current: 5400, target: 12000, percentage: 48, color: 'var(--color-primary-600)', iconType: 'car', priority: 'high' },
  { name: 'Emergency Fund', current: 6960, target: 12000, percentage: 58, color: 'var(--color-success-500)', iconType: 'shield', priority: 'high' },
  { name: 'Europe Trip', current: 7320, target: 12000, percentage: 61, color: 'var(--color-warning-500)', iconType: 'plane', priority: 'medium' },
  { name: 'HECS Debt', current: 1560, target: 12000, percentage: 13, color: 'var(--color-error-500)', iconType: 'graduation-cap', priority: 'low' }
]

// 🎨 Enhanced Professional Goals Icon Component - Design System Compliant
function GoalsIcon() {
  return (
    <div className="relative w-12 h-12 rounded-2xl flex items-center justify-center ring-1 ring-white/20"
         style={{
           background: 'linear-gradient(135deg, var(--color-need-500) 0%, var(--color-need-600) 100%)',
           boxShadow: 'var(--shadow-lg), 0 0 20px var(--color-need-500)'
         }}>
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="relative z-10">
        <path d="M12 2L15.09 8.26L22 9L16 14.74L17.18 21.02L12 18.77L6.82 21.02L8 14.74L2 9L8.91 8.26L12 2Z" fill="white"/>
      </svg>
      <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse"
           style={{ backgroundColor: 'var(--color-need-400)' }}></div>
    </div>
  )
}

// 💼 Professional Goal Icon Components - Button Style Design
function GoalIcon({ type, className = "" }: { type: string, className?: string }) {
  const iconComponents = {
    'car': <Car size={20} className="text-white" />,
    'shield': <Shield size={20} className="text-white" />,
    'plane': <Plane size={20} className="text-white" />,
    'graduation-cap': <GraduationCap size={20} className="text-white" />,
    'target': <Target size={20} className="text-white" />,
    'star': <Star size={20} className="text-white" />
  }

  return (
    <div className={`w-12 h-12 rounded-2xl flex items-center justify-center border transition-all duration-300 ${className}`}
         style={{
           background: 'linear-gradient(135deg, var(--color-need-500) 0%, var(--color-need-600) 100%)',
           borderColor: 'var(--color-need-600)',
           color: 'white',
           boxShadow: 'none'
         }}>
      {iconComponents[type as keyof typeof iconComponents] || iconComponents['star']}
    </div>
  )
}

export function GoalsWidget({
  totalGoalAmount = 21240,
  goals = defaultGoals,
  currency = '$'
}: GoalsWidgetProps) {
  const totalSaved = goals.reduce((sum, goal) => sum + goal.current, 0)
  const totalTarget = goals.reduce((sum, goal) => sum + goal.target, 0)
  const overallProgress = Math.round((totalSaved / totalTarget) * 100)

  // Show only first 3 active goals
  const displayedGoals = goals.slice(0, 3)

  return (
    <Card className="bg-white/80 backdrop-blur-sm border rounded-3xl transition-all duration-500 group hover:bg-white/90"
          style={{
            borderColor: 'var(--color-gray-200)',
            boxShadow: 'var(--shadow-lg)',
            '--tw-shadow-color': 'var(--color-gray-900)',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.borderColor = 'rgba(59, 130, 246, 0.2)'
            e.currentTarget.style.boxShadow = 'var(--shadow-xl)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.borderColor = 'var(--color-gray-200)'
            e.currentTarget.style.boxShadow = 'var(--shadow-lg)'
          }}>
      <CardHeader style={{ padding: 'var(--padding-card-lg) var(--padding-card-lg) var(--space-6) var(--padding-card-lg)' }}>
        <div className="flex items-start justify-between">
          <div className="flex items-center" style={{ gap: 'var(--space-4)' }}>
            <GoalsIcon />
            <div style={{ gap: 'var(--space-1)' }} className="space-y-1">
              <CardTitle className="text-heading-md font-semibold tracking-tight"
                         style={{ color: 'var(--color-gray-900)' }}>
                Financial Goals
              </CardTitle>
              <div className="text-label-md font-medium"
                   style={{ color: 'var(--color-gray-500)' }}>
                Savings targets
              </div>
            </div>
          </div>
          <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
            <button className="group/add relative overflow-hidden text-white font-semibold text-sm rounded-xl transition-all duration-300 flex items-center hover:scale-105 active:scale-95"
                    style={{
                      background: 'linear-gradient(135deg, var(--color-need-500) 0%, var(--color-need-600) 100%)',
                      padding: 'var(--space-3) var(--space-4)',
                      boxShadow: 'var(--shadow-lg), 0 0 20px var(--color-need-500)',
                      gap: 'var(--space-2)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, var(--color-need-600) 0%, var(--color-need-700) 100%)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-xl), 0 0 25px var(--color-need-500)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, var(--color-need-500) 0%, var(--color-need-600) 100%)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-lg), 0 0 20px var(--color-need-500)'
                    }}>
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover/add:opacity-100 transition-opacity duration-300"></div>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" className="relative z-10 group-hover/add:rotate-90 transition-transform duration-300">
                <path d="M12 5v14M5 12h14"/>
              </svg>
              <span className="relative z-10 tracking-tight">Add Goal</span>
            </button>
            <button className="group/edit relative bg-white border rounded-xl transition-all duration-300 hover:scale-105 active:scale-95"
                    style={{
                      borderColor: 'var(--color-gray-200)',
                      color: 'var(--color-gray-600)',
                      padding: 'var(--space-3)',
                      boxShadow: 'var(--shadow-sm)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--color-gray-50)'
                      e.currentTarget.style.borderColor = 'var(--color-gray-300)'
                      e.currentTarget.style.color = 'var(--color-gray-800)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-md)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white'
                      e.currentTarget.style.borderColor = 'var(--color-gray-200)'
                      e.currentTarget.style.color = 'var(--color-gray-600)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                    }}>
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="group-hover/edit:rotate-12 transition-transform duration-300">
                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
                <path d="m15 5 4 4"/>
              </svg>
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent style={{ padding: 'var(--padding-card-lg)', gap: 'var(--space-8)' }} className="space-y-8">
        {/* 💎 Enhanced Main Amount Section - Clean Design */}
        <div className="relative rounded-2xl border shadow-inner overflow-hidden"
             style={{
               background: 'linear-gradient(135deg, var(--color-need-50) 0%, var(--color-need-100) 100%)',
               borderColor: 'rgba(59, 130, 246, 0.2)',
               padding: 'var(--space-8)'
             }}>
          {/* Top border accent */}
          <div className="absolute top-0 left-0 right-0 h-1 rounded-t-2xl"
               style={{ background: 'linear-gradient(90deg, var(--color-need-500), var(--color-need-600))' }}></div>

          <div className="relative space-y-6" style={{ gap: 'var(--space-6)' }}>
            {/* Header Section */}
            <div style={{ gap: 'var(--space-3)' }} className="space-y-3">
              {/* Main Amount */}
              <div className="text-display-lg font-bold tracking-tighter leading-[1.1]"
                   style={{ color: 'var(--color-need-800)' }}>
                {currency}{totalSaved.toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </div>

              {/* Progress Indicator */}
              <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
                <div className="flex items-center justify-center w-6 h-6 rounded-lg"
                     style={{
                       backgroundColor: 'rgba(59, 130, 246, 0.15)',
                       color: 'var(--color-need-600)'
                     }}>
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                    <path d="M12 2L15.09 8.26L22 9L16 14.74L17.18 21.02L12 18.77L6.82 21.02L8 14.74L2 9L8.91 8.26L12 2Z"/>
                  </svg>
                </div>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-need-600)' }}>
                  {overallProgress}%
                </span>
                <span className="text-body-md font-medium"
                      style={{ color: 'var(--color-gray-600)' }}>
                  Overall Progress
                </span>
              </div>
            </div>

            {/* Target Goals Section */}
            <div className="rounded-2xl border"
                 style={{
                   background: 'rgba(255, 255, 255, 0.6)',
                   borderColor: 'rgba(59, 130, 246, 0.2)',
                   padding: 'var(--space-5)'
                 }}>
              <div className="text-label-md font-medium"
                   style={{
                     color: 'var(--color-gray-500)',
                     marginBottom: 'var(--space-2)'
                   }}>
                Total Target Goals
              </div>
              <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-need-600)' }}>
                  →
                </span>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-need-800)' }}>
                  {currency}{totalTarget.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 📊 Enhanced Goals List - Design System Compliant */}
        <div style={{ gap: 'var(--space-5)' }} className="space-y-5">
          <div className="flex items-center justify-between">
            <h4 className="text-heading-md font-semibold tracking-tight"
                style={{ color: 'var(--color-gray-900)' }}>
              Active Goals
            </h4>
            <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
              <span className="text-label-md font-medium"
                    style={{ color: 'var(--color-gray-500)' }}>
                {displayedGoals.length} of {goals.length} goals
              </span>
              <div className="w-2 h-2 rounded-full animate-pulse"
                   style={{ backgroundColor: 'var(--color-need-400)' }}></div>
            </div>
          </div>
          <div style={{ gap: 'var(--space-4)' }} className="space-y-4">
            {displayedGoals.map((goal, index) => (
              <div key={index} className="group relative bg-white rounded-2xl border transition-all duration-300 shadow-sm hover:shadow-md overflow-hidden"
                   style={{
                     borderColor: 'var(--color-gray-200)',
                   }}
                   onMouseEnter={(e) => {
                     e.currentTarget.style.borderColor = 'rgba(59, 130, 246, 0.2)'
                     e.currentTarget.style.boxShadow = 'var(--shadow-md)'
                   }}
                   onMouseLeave={(e) => {
                     e.currentTarget.style.borderColor = 'var(--color-gray-200)'
                     e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                   }}>

                {/* Top accent bar */}
                <div className="h-1"
                     style={{ background: 'linear-gradient(90deg, var(--color-need-200), var(--color-need-300))' }}></div>

                <div style={{ padding: 'var(--space-5)' }}>
                  {/* Header Row */}
                  <div className="flex items-center justify-between" style={{ marginBottom: 'var(--space-4)' }}>
                    <div className="flex items-center" style={{ gap: 'var(--space-3)' }}>
                      <GoalIcon
                        type={goal.iconType || 'star'}
                        className="group-hover:scale-105 transition-all duration-300 flex-shrink-0"
                      />
                      <div className="text-display-md font-semibold tracking-tight"
                           style={{ color: 'var(--color-gray-900)' }}>
                        {goal.name}
                      </div>
                    </div>

                    {/* Priority Badge */}
                    {goal.priority === 'high' && (
                      <div className="flex items-center rounded-full border"
                           style={{
                             backgroundColor: 'var(--color-error-50)',
                             borderColor: 'var(--color-error-200)',
                             padding: 'var(--space-1) var(--space-3)',
                             gap: 'var(--space-1)'
                           }}>
                        <div className="w-2 h-2 rounded-full"
                             style={{ backgroundColor: 'var(--color-error-500)' }}></div>
                        <span className="text-label-md font-medium"
                              style={{ color: 'var(--color-error-700)' }}>
                          High Priority
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Amount and Progress Row */}
                  <div className="flex items-end justify-between">
                    <div>
                      <div className="text-heading-md font-bold tracking-tight"
                           style={{ color: 'var(--color-gray-900)' }}>
                        {currency}{goal.current.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                      </div>
                      <div className="text-label-md font-medium"
                           style={{ color: 'var(--color-gray-500)' }}>
                        of {currency}{goal.target.toLocaleString()} target
                      </div>
                    </div>

                    {/* Progress Badge */}
                    <div className="flex items-center rounded-lg"
                         style={{
                           backgroundColor: 'var(--color-need-50)',
                           padding: 'var(--space-2) var(--space-3)',
                           gap: 'var(--space-1)'
                         }}>
                      <span className="text-label-md font-bold"
                            style={{ color: 'var(--color-need-600)' }}>
                        {goal.percentage}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
