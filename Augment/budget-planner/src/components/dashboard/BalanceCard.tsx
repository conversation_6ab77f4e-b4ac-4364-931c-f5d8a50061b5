'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'

interface BalanceCardProps {
  currentBalance: number
  expectedExpense: number
  expectedBalance: number
  currency?: string
}

// 🎨 Enhanced Professional Balance Icon Component - Design System Compliant
function BalanceIcon() {
  return (
    <div className="relative w-12 h-12 rounded-2xl flex items-center justify-center ring-1 ring-white/20"
         style={{
           background: 'linear-gradient(135deg, var(--color-need-500) 0%, var(--color-need-600) 100%)',
           boxShadow: 'var(--shadow-lg), 0 0 20px var(--color-need-500)'
         }}>
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="relative z-10">
        <path d="M12 1L21.5 6.5V17.5L12 23L2.5 17.5V6.5L12 1Z" stroke="white" strokeWidth="2" fill="none"/>
        <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
        <circle cx="12" cy="12" r="2" fill="white" fillOpacity="0.8"/>
      </svg>
      <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse"
           style={{ backgroundColor: 'var(--color-need-400)' }}></div>
    </div>
  )
}

export function BalanceCard({
  currentBalance = 1002.35,
  expectedExpense = 1200,
  expectedBalance = -197.65,
  currency = '$'
}: BalanceCardProps) {
  const balanceStatus = expectedBalance >= 0 ? 'positive' : 'negative'
  const balancePercentage = Math.abs((expectedBalance / currentBalance) * 100)

  return (
    <Card className="bg-white/80 backdrop-blur-sm border rounded-3xl transition-all duration-500 group hover:bg-white/90"
          style={{
            borderColor: 'var(--color-gray-200)',
            boxShadow: 'var(--shadow-lg)',
            '--tw-shadow-color': 'var(--color-gray-900)',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.borderColor = 'var(--color-need-200)'
            e.currentTarget.style.boxShadow = 'var(--shadow-xl)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.borderColor = 'var(--color-gray-200)'
            e.currentTarget.style.boxShadow = 'var(--shadow-lg)'
          }}>
      <CardHeader style={{ padding: 'var(--padding-card-lg) var(--padding-card-lg) var(--space-6) var(--padding-card-lg)' }}>
        <div className="flex items-start justify-between">
          <div className="flex items-center" style={{ gap: 'var(--space-4)' }}>
            <BalanceIcon />
            <div style={{ gap: 'var(--space-1)' }} className="space-y-1">
              <CardTitle className="text-heading-md font-semibold tracking-tight"
                         style={{ color: 'var(--color-gray-900)' }}>
                Current Balance
              </CardTitle>
              <div className="text-label-md font-medium"
                   style={{ color: 'var(--color-gray-500)' }}>
                Available funds
              </div>
            </div>
          </div>
          <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
            <button className="group/view relative overflow-hidden text-white font-semibold text-sm rounded-xl transition-all duration-300 flex items-center hover:scale-105 active:scale-95"
                    style={{
                      background: 'linear-gradient(135deg, var(--color-need-500) 0%, var(--color-need-600) 100%)',
                      padding: 'var(--space-3) var(--space-4)',
                      boxShadow: 'var(--shadow-lg), 0 0 20px var(--color-need-500)',
                      gap: 'var(--space-2)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, var(--color-need-600) 0%, var(--color-need-700) 100%)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-xl), 0 0 25px var(--color-need-500)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, var(--color-need-500) 0%, var(--color-need-600) 100%)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-lg), 0 0 20px var(--color-need-500)'
                    }}>
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover/view:opacity-100 transition-opacity duration-300"></div>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" className="relative z-10 group-hover/view:scale-110 transition-transform duration-300">
                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/>
                <circle cx="12" cy="12" r="3"/>
              </svg>
              <span className="relative z-10 tracking-tight">View Details</span>
            </button>
            <button className="group/settings relative bg-white border rounded-xl transition-all duration-300 hover:scale-105 active:scale-95"
                    style={{
                      borderColor: 'var(--color-gray-200)',
                      color: 'var(--color-gray-600)',
                      padding: 'var(--space-3)',
                      boxShadow: 'var(--shadow-sm)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--color-gray-50)'
                      e.currentTarget.style.borderColor = 'var(--color-gray-300)'
                      e.currentTarget.style.color = 'var(--color-gray-800)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-md)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white'
                      e.currentTarget.style.borderColor = 'var(--color-gray-200)'
                      e.currentTarget.style.color = 'var(--color-gray-600)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                    }}>
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="group-hover/settings:rotate-90 transition-transform duration-300">
                <circle cx="12" cy="12" r="3"/>
                <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24"/>
              </svg>
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent style={{ padding: 'var(--padding-card-lg)', gap: 'var(--space-8)' }} className="space-y-8">
        {/* 💎 Enhanced Current Balance Section - Clean Design */}
        <div className="relative rounded-2xl border shadow-inner overflow-hidden"
             style={{
               background: 'linear-gradient(135deg, var(--color-need-50) 0%, var(--color-need-100) 100%)',
               borderColor: 'var(--color-need-200)',
               padding: 'var(--space-8)'
             }}>
          {/* Top border accent */}
          <div className="absolute top-0 left-0 right-0 h-1 rounded-t-2xl"
               style={{ background: 'linear-gradient(90deg, var(--color-need-500), var(--color-need-600))' }}></div>

          <div className="relative space-y-6" style={{ gap: 'var(--space-6)' }}>
            {/* Header Section */}
            <div style={{ gap: 'var(--space-3)' }} className="space-y-3">
              {/* Main Amount */}
              <div className="text-display-lg font-bold tracking-tighter leading-[1.1]"
                   style={{ color: 'var(--color-need-800)' }}>
                {currency}{currentBalance.toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </div>

              {/* Status Indicator */}
              <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
                <div className="flex items-center justify-center w-6 h-6 rounded-lg"
                     style={{
                       backgroundColor: 'rgba(34, 197, 94, 0.15)',
                       color: 'var(--color-need-600)'
                     }}>
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                    <path d="M9 12L11 14L15 10"/>
                  </svg>
                </div>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-need-600)' }}>
                  Excellent
                </span>
                <span className="text-body-md font-medium"
                      style={{ color: 'var(--color-gray-600)' }}>
                  Liquidity Status
                </span>
              </div>
            </div>

            {/* Expected Expense Section */}
            <div className="rounded-2xl border"
                 style={{
                   background: 'rgba(255, 255, 255, 0.6)',
                   borderColor: 'rgba(251, 146, 60, 0.2)',
                   padding: 'var(--space-5)'
                 }}>
              <div className="text-label-md font-medium"
                   style={{
                     color: 'var(--color-gray-500)',
                     marginBottom: 'var(--space-2)'
                   }}>
                Expected Expense This Month
              </div>
              <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-warning-600)' }}>
                  -
                </span>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-warning-800)' }}>
                  {currency}{expectedExpense.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 📊 Enhanced Expected Balance Forecast - Design System Compliant */}
        <div style={{ gap: 'var(--space-5)' }} className="space-y-5">
          <div className="flex items-center justify-between">
            <h4 className="text-heading-md font-semibold tracking-tight"
                style={{ color: 'var(--color-gray-900)' }}>
              Month-End Forecast
            </h4>
            <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
              <span className="inline-flex items-center rounded-xl text-label-md font-semibold border shadow-sm"
                    style={{
                      backgroundColor: balanceStatus === 'positive' ? 'var(--color-success-100)' : 'var(--color-error-100)',
                      color: balanceStatus === 'positive' ? 'var(--color-success-700)' : 'var(--color-error-700)',
                      borderColor: balanceStatus === 'positive' ? 'var(--color-success-200)' : 'var(--color-error-200)',
                      padding: 'var(--space-2) var(--space-4)'
                    }}>
                {balanceStatus === 'positive' ? 'Surplus Expected' : 'Deficit Warning'}
              </span>
            </div>
          </div>

          <div className="relative bg-white/60 backdrop-blur-sm rounded-2xl border shadow-sm"
               style={{
                 borderColor: 'var(--color-gray-100)',
                 padding: 'var(--space-6)'
               }}>
            <div className="flex items-start justify-between" style={{ marginBottom: 'var(--space-6)' }}>
              <div style={{ gap: 'var(--space-2)' }} className="space-y-2">
                <div className="text-label-md font-medium"
                     style={{ color: 'var(--color-gray-500)' }}>
                  Projected Balance
                </div>
                <div className="text-display-lg font-bold tracking-tighter"
                     style={{ color: expectedBalance < 0 ? 'var(--color-error-600)' : 'var(--color-success-600)' }}>
                  {expectedBalance < 0 ? '-' : '+'}{currency}{Math.abs(expectedBalance).toFixed(2)}
                </div>
              </div>
              <div className="text-right" style={{ gap: 'var(--space-2)' }} className="space-y-2">
                <div className="text-label-md font-medium"
                     style={{ color: 'var(--color-gray-500)' }}>
                  Net Change
                </div>
                <div className="flex items-center justify-end" style={{ gap: 'var(--space-1)' }}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5"
                       style={{ color: expectedBalance < 0 ? 'var(--color-error-600)' : 'var(--color-success-600)' }}>
                    {expectedBalance >= 0 ? (
                      <path d="M7 17L17 7M17 7H7M17 7V17"/>
                    ) : (
                      <path d="M17 17L7 7M7 7H17M7 7V17"/>
                    )}
                  </svg>
                  <span className="text-heading-lg font-bold"
                        style={{ color: expectedBalance < 0 ? 'var(--color-error-600)' : 'var(--color-success-600)' }}>
                    {balancePercentage.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>

            {/* 💎 Enhanced Progress Visualization */}
            <div style={{ gap: 'var(--space-4)' }} className="space-y-4">
              <div className="w-full rounded-full h-3 shadow-inner"
                   style={{ backgroundColor: 'var(--color-gray-200)' }}>
                <div className="h-3 rounded-full transition-all duration-700 ease-out shadow-sm"
                     style={{
                       background: expectedBalance >= 0
                         ? 'linear-gradient(to right, var(--color-success-400), var(--color-success-500), var(--color-success-600))'
                         : 'linear-gradient(to right, var(--color-error-400), var(--color-error-500), var(--color-error-600))',
                       width: `${Math.min(Math.abs(balancePercentage), 100)}%`
                     }}></div>
              </div>

              <div className="flex justify-between items-center">
                <div className="text-center">
                  <div className="text-label-md font-medium"
                       style={{ color: 'var(--color-gray-500)' }}>
                    Current
                  </div>
                  <div className="text-body-lg font-semibold"
                       style={{ color: 'var(--color-gray-900)' }}>
                    {currency}{currentBalance.toFixed(2)}
                  </div>
                </div>
                <div className="flex-1 flex justify-center">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
                       style={{ color: 'var(--color-gray-400)' }}>
                    <path d="M5 12h14M12 5l7 7-7 7"/>
                  </svg>
                </div>
                <div className="text-center">
                  <div className="text-label-md font-medium"
                       style={{ color: 'var(--color-gray-500)' }}>
                    Projected
                  </div>
                  <div className="text-body-lg font-semibold"
                       style={{ color: expectedBalance < 0 ? 'var(--color-error-600)' : 'var(--color-success-600)' }}>
                    {currency}{(currentBalance + expectedBalance).toFixed(2)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
