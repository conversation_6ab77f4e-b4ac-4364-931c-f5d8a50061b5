import Link from 'next/link'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'

interface Transaction {
  id: string
  description: string
  amount: number
  category: string
  date: string
  type: 'expense' | 'income'
}

interface RecentTransactionsWidgetProps {
  transactions: Transaction[]
  currency?: string
}

export function RecentTransactionsWidget({ 
  transactions, 
  currency = '$' 
}: RecentTransactionsWidgetProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (date.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    }
  }

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      'Food & Dining': '🍔',
      'Groceries': '🛒',
      'Transportation': '🚗',
      'Gas': '⛽',
      'Entertainment': '🎬',
      'Shopping': '🛍️',
      'Income': '💰',
      'Coffee': '☕',
      'Restaurant': '🍽️',
      'Utilities': '🏠',
      'Healthcare': '🏥',
      'Education': '📚'
    }
    return icons[category] || '💳'
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <span className="text-xl mr-2">💳</span>
            Recent Transactions
          </CardTitle>
          <Link href="/expenses">
            <Button variant="ghost" size="sm" className="text-blue-600">
              View All
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {transactions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">📝</div>
              <p className="text-sm">No transactions yet</p>
              <p className="text-xs text-gray-400">Add your first expense to get started</p>
            </div>
          ) : (
            transactions.slice(0, 5).map((transaction, index) => (
              <div key={transaction.id}>
                <div className="flex items-center justify-between py-3">
                <div className="flex items-center space-x-3">
                  <div className="text-lg">
                    {getCategoryIcon(transaction.category)}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 text-sm">
                      {transaction.description}
                    </p>
                    <p className="text-xs text-gray-500">
                      {transaction.category}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-medium text-sm ${
                    transaction.type === 'income' 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {transaction.type === 'income' ? '+' : '-'}
                    {currency}{Math.abs(transaction.amount).toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatDate(transaction.date)}
                  </p>
                </div>
                </div>
                {index < transactions.slice(0, 5).length - 1 && <Separator className="my-2" />}
              </div>
            ))
          )}
          
          {transactions.length > 5 && (
            <div className="pt-3 text-center">
              <Link href="/expenses">
                <Button variant="outline" size="sm" className="w-full">
                  View {transactions.length - 5} More Transactions
                </Button>
              </Link>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
