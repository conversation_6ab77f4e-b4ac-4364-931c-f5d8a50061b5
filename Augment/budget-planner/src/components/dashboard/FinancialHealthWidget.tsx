import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface FinancialHealthWidgetProps {
  score: number
  status: string
  nextGoal: string
  recommendations: string[]
}

export function FinancialHealthWidget({
  score,
  status,
  nextGoal,
  recommendations
}: FinancialHealthWidgetProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const getScoreBackground = (score: number) => {
    if (score >= 80) return 'from-green-50 to-green-100 border-green-200'
    if (score >= 60) return 'from-yellow-50 to-yellow-100 border-yellow-200'
    if (score >= 40) return 'from-orange-50 to-orange-100 border-orange-200'
    return 'from-red-50 to-red-100 border-red-200'
  }

  const getStatusIcon = (score: number) => {
    if (score >= 80) return '🎯'
    if (score >= 60) return '👍'
    if (score >= 40) return '⚠️'
    return '🚨'
  }

  const circumference = 2 * Math.PI * 45
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (score / 100) * circumference

  return (
    <Card className={`bg-gradient-to-br ${getScoreBackground(score)}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center">
          <span className="text-xl mr-2">🎯</span>
          Financial Health Score
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Score Circle */}
          <div className="flex items-center justify-center">
            <div className="relative w-32 h-32">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
                {/* Background circle */}
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  className="text-white opacity-30"
                />
                {/* Progress circle */}
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  className={getScoreColor(score)}
                  style={{
                    transition: 'stroke-dashoffset 1s ease-in-out'
                  }}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className={`text-3xl font-bold ${getScoreColor(score)}`}>
                    {score}
                  </div>
                  <div className="text-xs text-gray-600">out of 100</div>
                </div>
              </div>
            </div>
          </div>

          {/* Status */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <span className="text-2xl">{getStatusIcon(score)}</span>
              <span className={`text-lg font-semibold ${getScoreColor(score)}`}>
                {status}
              </span>
            </div>
            <p className="text-sm text-gray-700">
              Your financial health is looking {status.toLowerCase()}!
            </p>
          </div>

          {/* Next Goal */}
          <div className="bg-white bg-opacity-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-sm">🎯</span>
              <span className="text-sm font-medium text-gray-800">Next Goal</span>
            </div>
            <p className="text-sm text-gray-700">{nextGoal}</p>
          </div>

          {/* Quick Recommendations */}
          {recommendations.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-800 flex items-center">
                <span className="mr-2">💡</span>
                Quick Tips
              </div>
              <div className="space-y-1">
                {recommendations.slice(0, 2).map((recommendation, index) => (
                  <div key={index} className="text-xs text-gray-700 bg-white bg-opacity-50 rounded p-2">
                    {recommendation}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Button */}
          <Button variant="outline" className="w-full bg-white bg-opacity-50 hover:bg-white hover:bg-opacity-75">
            View Detailed Analysis
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
