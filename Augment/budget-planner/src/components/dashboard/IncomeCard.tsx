'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'

interface IncomeBreakdown {
  category: string
  amount: number
  iconType?: 'part-time' | 'paycheque' | 'side-hustle' | 'freelance' | 'investment' | 'rental' | 'business' | 'other'
  trend?: 'up' | 'down' | 'stable'
}

interface IncomeCardProps {
  totalIncome: number
  extraAmount: number
  breakdown: IncomeBreakdown[]
  currency?: string
}

// 🎨 Enhanced Professional Icon Component - Design System Compliant
function IncomeIcon() {
  return (
    <div className="relative w-12 h-12 rounded-2xl flex items-center justify-center ring-1 ring-white/20"
         style={{
           background: 'linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%)',
           boxShadow: 'var(--shadow-lg), 0 0 20px var(--color-success-500)'
         }}>
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="relative z-10">
        <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="white" fillOpacity="0.95"/>
        <path d="M19 15L19.91 18.26L23 19L19.91 19.74L19 23L18.09 19.74L15 19L18.09 18.26L19 15Z" fill="white" fillOpacity="0.8"/>
        <path d="M5 6L5.91 9.26L9 10L5.91 10.74L5 14L4.09 10.74L1 10L4.09 9.26L5 6Z" fill="white" fillOpacity="0.8"/>
      </svg>
      <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse"
           style={{ backgroundColor: 'var(--color-success-400)' }}></div>
    </div>
  )
}

// 💼 Professional Heroicons - Clean Flat Design
function IncomeSourceIcon({ type, className = "" }: { type: string, className?: string }) {
  const iconComponents = {
    'part-time': (
      // Heroicon: user-circle
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    ),
    'paycheque': (
      // Heroicon: banknotes
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z" />
      </svg>
    ),
    'side-hustle': (
      // Heroicon: star
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
      </svg>
    ),
    'freelance': (
      // Heroicon: briefcase
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 00.75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 00-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0112 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 01-.673-.38m0 0A2.18 2.18 0 013 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 013.413-.387m7.5 0V5.25A2.25 2.25 0 0013.5 3h-3a2.25 2.25 0 00-2.25 2.25v.894m7.5 0a48.667 48.667 0 00-7.5 0M12 12.75h.008v.008H12v-.008z" />
      </svg>
    ),
    'investment': (
      // Heroicon: arrow-trending-up
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" />
      </svg>
    ),
    'rental': (
      // Heroicon: home
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
      </svg>
    ),
    'business': (
      // Heroicon: building-storefront
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 21v-7.5a.75.75 0 01.75-.75h3a.75.75 0 01.75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349m-16.5 11.65V9.35m0 0a3.001 3.001 0 003.75-.615A2.993 2.993 0 009.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 002.25 1.016c.896 0 1.7-.393 2.25-1.016a3.001 3.001 0 003.75.614m-16.5 0a3.004 3.004 0 01-.621-1.72L1.318 5.917A3 3 0 014.25 3h15.5a3 3 0 012.932 2.917l-.853 1.713a3.004 3.004 0 01-.621 1.72m-16.5 0h16.5" />
      </svg>
    ),
    'other': (
      // Heroicon: question-mark-circle
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
      </svg>
    )
  }

  return (
    <div className={`w-12 h-12 rounded-2xl flex items-center justify-center border transition-all duration-300 ${className}`}
         style={{
           background: 'linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%)',
           borderColor: 'var(--color-success-600)',
           color: 'white',
           boxShadow: 'none'
         }}>
      {iconComponents[type as keyof typeof iconComponents] || iconComponents.other}
    </div>
  )
}

export function IncomeCard({
  totalIncome = 5502.05,
  extraAmount = 250.00,
  breakdown = [
    { category: 'Part-Time', amount: 500.00, iconType: 'part-time', trend: 'stable' },
    { category: 'Paycheque', amount: 3502.45, iconType: 'paycheque', trend: 'up' },
    { category: 'Side Hustle', amount: 1500.00, iconType: 'side-hustle', trend: 'up' }
  ],
  currency = '$'
}: IncomeCardProps) {
  return (
    <Card className="bg-white/80 backdrop-blur-sm border rounded-3xl transition-all duration-500 group hover:bg-white/90"
          style={{
            borderColor: 'var(--color-gray-200)',
            boxShadow: 'var(--shadow-lg)',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.borderColor = 'var(--color-success-200)'
            e.currentTarget.style.boxShadow = 'var(--shadow-xl)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.borderColor = 'var(--color-gray-200)'
            e.currentTarget.style.boxShadow = 'var(--shadow-lg)'
          }}>
      <CardHeader style={{ padding: 'var(--padding-card-lg) var(--padding-card-lg) var(--space-6) var(--padding-card-lg)' }}>
        <div className="flex items-start justify-between">
          <div className="flex items-center" style={{ gap: 'var(--space-4)' }}>
            <IncomeIcon />
            <div style={{ gap: 'var(--space-1)' }} className="space-y-1">
              <CardTitle className="text-heading-md font-semibold tracking-tight"
                         style={{ color: 'var(--color-gray-900)' }}>
                Total Income
              </CardTitle>
              <div className="text-label-md font-medium"
                   style={{ color: 'var(--color-gray-500)' }}>
                Monthly earnings
              </div>
            </div>
          </div>
          <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
            <button className="group/add relative overflow-hidden text-white font-semibold text-sm rounded-xl transition-all duration-300 flex items-center hover:scale-105 active:scale-95"
                    style={{
                      background: 'linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%)',
                      padding: 'var(--space-3) var(--space-4)',
                      boxShadow: 'var(--shadow-lg), 0 0 20px var(--color-success-500)',
                      gap: 'var(--space-2)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, var(--color-success-600) 0%, var(--color-success-700) 100%)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-xl), 0 0 25px var(--color-success-500)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-lg), 0 0 20px var(--color-success-500)'
                    }}>
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover/add:opacity-100 transition-opacity duration-300"></div>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" className="relative z-10 group-hover/add:rotate-90 transition-transform duration-300">
                <path d="M12 5v14M5 12h14"/>
              </svg>
              <span className="relative z-10 tracking-tight">Add Income</span>
            </button>
            <button className="group/edit relative bg-white border rounded-xl transition-all duration-300 hover:scale-105 active:scale-95"
                    style={{
                      borderColor: 'var(--color-gray-200)',
                      color: 'var(--color-gray-600)',
                      padding: 'var(--space-3)',
                      boxShadow: 'var(--shadow-sm)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--color-gray-50)'
                      e.currentTarget.style.borderColor = 'var(--color-gray-300)'
                      e.currentTarget.style.color = 'var(--color-gray-800)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-md)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white'
                      e.currentTarget.style.borderColor = 'var(--color-gray-200)'
                      e.currentTarget.style.color = 'var(--color-gray-600)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                    }}>
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="group-hover/edit:rotate-12 transition-transform duration-300">
                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
                <path d="m15 5 4 4"/>
              </svg>
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent style={{ padding: 'var(--padding-card-lg)', gap: 'var(--space-8)' }} className="space-y-8">
        {/* 💎 Enhanced Main Amount Section - Clean Design */}
        <div className="relative rounded-2xl border shadow-inner overflow-hidden"
             style={{
               background: 'linear-gradient(135deg, var(--color-success-50) 0%, var(--color-success-100) 100%)',
               borderColor: 'var(--color-success-200)',
               padding: 'var(--space-8)'
             }}>
          {/* Top border accent */}
          <div className="absolute top-0 left-0 right-0 h-1 rounded-t-2xl"
               style={{ background: 'linear-gradient(90deg, var(--color-success-500), var(--color-success-600))' }}></div>

          <div className="relative space-y-6" style={{ gap: 'var(--space-6)' }}>
            {/* Header Section */}
            <div style={{ gap: 'var(--space-3)' }} className="space-y-3">
              {/* Main Amount */}
              <div className="text-display-lg font-bold tracking-tighter leading-[1.1]"
                   style={{ color: 'var(--color-success-800)' }}>
                {currency}{totalIncome.toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </div>

              {/* Growth Indicator */}
              <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
                <div className="flex items-center justify-center w-6 h-6 rounded-lg"
                     style={{
                       backgroundColor: 'rgba(34, 197, 94, 0.15)',
                       color: 'var(--color-success-600)'
                     }}>
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                    <path d="M7 17L17 7M17 7H7M17 7V17"/>
                  </svg>
                </div>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-success-600)' }}>
                  4.8%
                </span>
                <span className="text-body-md font-medium"
                      style={{ color: 'var(--color-gray-600)' }}>
                  Monthly Growth
                </span>
              </div>
            </div>

            {/* Extra Earned Section */}
            <div className="rounded-2xl border"
                 style={{
                   background: 'rgba(255, 255, 255, 0.6)',
                   borderColor: 'rgba(34, 197, 94, 0.2)',
                   padding: 'var(--space-5)'
                 }}>
              <div className="text-label-md font-medium"
                   style={{
                     color: 'var(--color-gray-500)',
                     marginBottom: 'var(--space-2)'
                   }}>
                Extra Earned This Month
              </div>
              <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-success-600)' }}>
                  +
                </span>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-success-800)' }}>
                  {currency}{extraAmount.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 📊 Enhanced Income Breakdown - Design System Compliant */}
        <div style={{ gap: 'var(--space-5)' }} className="space-y-5">
          <div className="flex items-center justify-between">
            <h4 className="text-heading-md font-semibold tracking-tight"
                style={{ color: 'var(--color-gray-900)' }}>
              Income Sources
            </h4>
            <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
              <span className="text-label-md font-medium"
                    style={{ color: 'var(--color-gray-500)' }}>
                {breakdown.length} active sources
              </span>
              <div className="w-2 h-2 rounded-full animate-pulse"
                   style={{ backgroundColor: 'var(--color-success-400)' }}></div>
            </div>
          </div>
          <div style={{ gap: 'var(--space-4)' }} className="space-y-4">
            {breakdown.map((item, index) => (
              <div key={index} className="group relative bg-white rounded-2xl border transition-all duration-300 shadow-sm hover:shadow-md overflow-hidden"
                   style={{
                     borderColor: 'var(--color-gray-200)',
                   }}
                   onMouseEnter={(e) => {
                     e.currentTarget.style.borderColor = 'var(--color-success-200)'
                     e.currentTarget.style.boxShadow = 'var(--shadow-md)'
                   }}
                   onMouseLeave={(e) => {
                     e.currentTarget.style.borderColor = 'var(--color-gray-200)'
                     e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                   }}>

                {/* Top accent bar */}
                <div className="h-1"
                     style={{ background: 'linear-gradient(90deg, var(--color-success-200), var(--color-success-300))' }}></div>

                <div style={{ padding: 'var(--space-5)' }}>
                  {/* Header Row */}
                  <div className="flex items-center justify-between" style={{ marginBottom: 'var(--space-4)' }}>
                    <div className="flex items-center" style={{ gap: 'var(--space-3)' }}>
                      <IncomeSourceIcon
                        type={item.iconType || 'other'}
                        className="group-hover:scale-105 transition-all duration-300 flex-shrink-0"
                      />
                      <div className="text-display-md font-semibold tracking-tight"
                           style={{ color: 'var(--color-gray-900)' }}>
                        {item.category}
                      </div>
                    </div>

                    {/* Status Badge */}
                    {item.trend === 'up' && (
                      <div className="flex items-center rounded-full border"
                           style={{
                             backgroundColor: 'var(--color-success-50)',
                             borderColor: 'var(--color-success-200)',
                             padding: 'var(--space-1) var(--space-3)',
                             gap: 'var(--space-1)'
                           }}>
                        <div className="w-2 h-2 rounded-full"
                             style={{ backgroundColor: 'var(--color-success-500)' }}></div>
                        <span className="text-label-md font-medium"
                              style={{ color: 'var(--color-success-700)' }}>
                          Growing
                        </span>
                      </div>
                    )}
                    {item.trend === 'stable' && (
                      <div className="flex items-center rounded-full border"
                           style={{
                             backgroundColor: 'var(--color-gray-50)',
                             borderColor: 'var(--color-gray-200)',
                             padding: 'var(--space-1) var(--space-3)',
                             gap: 'var(--space-1)'
                           }}>
                        <div className="w-2 h-2 rounded-full"
                             style={{ backgroundColor: 'var(--color-gray-400)' }}></div>
                        <span className="text-label-md font-medium"
                              style={{ color: 'var(--color-gray-600)' }}>
                          Stable
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Amount and Growth Row */}
                  <div className="flex items-end justify-between">
                    <div>
                      <div className="text-heading-md font-bold tracking-tight"
                           style={{ color: 'var(--color-gray-900)' }}>
                        {currency}{item.amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                      </div>
                      <div className="text-label-md font-medium"
                           style={{ color: 'var(--color-gray-500)' }}>
                        {Math.round((item.amount / totalIncome) * 100)}% of total
                      </div>
                    </div>

                    {/* Growth Indicator */}
                    {item.trend === 'up' && (
                      <div className="flex items-center rounded-lg"
                           style={{
                             backgroundColor: 'var(--color-success-50)',
                             padding: 'var(--space-2) var(--space-3)',
                             gap: 'var(--space-1)'
                           }}>
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5"
                             style={{ color: 'var(--color-success-600)' }}>
                          <path d="M7 17L17 7M17 7H7M17 7V17"/>
                        </svg>
                        <span className="text-label-md font-bold"
                              style={{ color: 'var(--color-success-600)' }}>
                          +2.8%
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
