'use client'

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'

interface ExpenseItem {
  category: string
  subcategory: string
  amount: number
  iconType?: 'housing' | 'food' | 'transport' | 'utilities' | 'healthcare' | 'entertainment' | 'shopping' | 'education' | 'other'
  budgetType: 'NEED' | 'WANT' | 'SMILE'
  percentage?: number
}

interface ExpenseCardProps {
  totalExpense: number
  overspentAmount: number
  topExpenses: ExpenseItem[]
  currency?: string
}

// 🎨 Enhanced Professional Expense Icon Component - Design System Compliant
function ExpenseIcon() {
  return (
    <div className="relative w-12 h-12 rounded-2xl flex items-center justify-center ring-1 ring-white/20"
         style={{
           background: 'linear-gradient(135deg, var(--color-wants-500) 0%, var(--color-wants-600) 100%)',
           boxShadow: 'var(--shadow-lg), 0 0 20px var(--color-wants-500)'
         }}>
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="relative z-10">
        <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
        <path d="M12 8V12L14.5 14.5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
      <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse"
           style={{ backgroundColor: 'var(--color-warning-400)' }}></div>
    </div>
  )
}

// 💰 Professional Heroicons - Clean Flat Design for Expenses
function ExpenseCategoryIcon({ type, budgetType, className = "" }: { type: string, budgetType: 'NEED' | 'WANT' | 'SMILE', className?: string }) {
  const iconComponents = {
    'housing': (
      // Heroicon: home
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
      </svg>
    ),
    'food': (
      // Heroicon: shopping-cart
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
      </svg>
    ),
    'transport': (
      // Heroicon: truck
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 18.75a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 01-1.125-1.125V14.25m15.75 4.5a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h1.125A1.125 1.125 0 0021 17.25v-6.75m-15.75 0V8.25A2.25 2.25 0 017.5 6h1.5m0 0V5.25A2.25 2.25 0 0111.25 3h1.5a2.25 2.25 0 012.25 2.25V6h1.5a2.25 2.25 0 012.25 2.25v6.75" />
      </svg>
    ),
    'utilities': (
      // Heroicon: bolt
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
      </svg>
    ),
    'healthcare': (
      // Heroicon: heart
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
      </svg>
    ),
    'entertainment': (
      // Heroicon: film
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 01-1.125-1.125M3.375 19.5h7.5c.621 0 1.125-.504 1.125-1.125m-9.75 0V5.625m0 0A2.25 2.25 0 015.625 3.375h4.5c.621 0 1.125.504 1.125 1.125v2.25m-9.75 0V8.25m0 0V12m9.75-3.75h3.375c.621 0 1.125.504 1.125 1.125v2.25m0 0V12m0 0v6.75m0 0V15m0 0h3.375c.621 0 1.125.504 1.125 1.125v2.25M12 10.5V8.25m0 0V5.625M12 8.25h.008v.008H12V8.25z" />
      </svg>
    ),
    'shopping': (
      // Heroicon: shopping-bag
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
      </svg>
    ),
    'education': (
      // Heroicon: academic-cap
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443m-7.007 11.55A5.981 5.981 0 006.75 15.75v-1.5" />
      </svg>
    ),
    'other': (
      // Heroicon: question-mark-circle
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
      </svg>
    )
  }

  return (
    <div className={`w-12 h-12 rounded-2xl flex items-center justify-center border transition-all duration-300 ${className}`}
         style={{
           background: 'linear-gradient(135deg, var(--color-wants-500) 0%, var(--color-wants-600) 100%)',
           borderColor: 'var(--color-wants-600)',
           color: 'white',
           boxShadow: 'none'
         }}>
      {iconComponents[type as keyof typeof iconComponents] || iconComponents.other}
    </div>
  )
}

export function ExpenseCard({
  totalExpense = 8750.00,
  overspentAmount = 1209.45,
  topExpenses = [
    { category: 'NEED', subcategory: 'Rent/Mortgage', amount: 2800.00, iconType: 'housing', budgetType: 'NEED', percentage: 32 },
    { category: 'NEED', subcategory: 'Groceries & Food', amount: 1200.00, iconType: 'food', budgetType: 'NEED', percentage: 14 },
    { category: 'NEED', subcategory: 'Transportation', amount: 950.00, iconType: 'transport', budgetType: 'NEED', percentage: 11 }
  ],
  currency = '$'
}: ExpenseCardProps) {
  return (
    <Card className="bg-white/80 backdrop-blur-sm border rounded-3xl transition-all duration-500 group hover:bg-white/90"
          style={{
            borderColor: 'var(--color-gray-200)',
            boxShadow: 'var(--shadow-lg)',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.borderColor = 'rgba(220, 38, 38, 0.2)'
            e.currentTarget.style.boxShadow = 'var(--shadow-xl)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.borderColor = 'var(--color-gray-200)'
            e.currentTarget.style.boxShadow = 'var(--shadow-lg)'
          }}>
      <CardHeader style={{ padding: 'var(--padding-card-lg) var(--padding-card-lg) var(--space-6) var(--padding-card-lg)' }}>
        <div className="flex items-start justify-between">
          <div className="flex items-center" style={{ gap: 'var(--space-4)' }}>
            <ExpenseIcon />
            <div style={{ gap: 'var(--space-1)' }} className="space-y-1">
              <CardTitle className="text-heading-md font-semibold tracking-tight"
                         style={{ color: 'var(--color-gray-900)' }}>
                Total Expense
              </CardTitle>
              <div className="text-label-md font-medium"
                   style={{ color: 'var(--color-gray-500)' }}>
                Monthly spending
              </div>
            </div>
          </div>
          <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
            <button className="group/add relative overflow-hidden text-white font-semibold text-sm rounded-xl transition-all duration-300 flex items-center hover:scale-105 active:scale-95"
                    style={{
                      background: 'linear-gradient(135deg, var(--color-wants-500) 0%, var(--color-wants-600) 100%)',
                      padding: 'var(--space-3) var(--space-4)',
                      boxShadow: 'var(--shadow-lg), 0 0 20px var(--color-wants-500)',
                      gap: 'var(--space-2)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, var(--color-wants-600) 0%, var(--color-wants-700) 100%)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-xl), 0 0 25px var(--color-wants-500)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, var(--color-wants-500) 0%, var(--color-wants-600) 100%)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-lg), 0 0 20px var(--color-wants-500)'
                    }}>
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover/add:opacity-100 transition-opacity duration-300"></div>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" className="relative z-10 group-hover/add:rotate-90 transition-transform duration-300">
                <path d="M12 5v14M5 12h14"/>
              </svg>
              <span className="relative z-10 tracking-tight">Add Expense</span>
            </button>
            <button className="group/edit relative bg-white border rounded-xl transition-all duration-300 hover:scale-105 active:scale-95"
                    style={{
                      borderColor: 'var(--color-gray-200)',
                      color: 'var(--color-gray-600)',
                      padding: 'var(--space-3)',
                      boxShadow: 'var(--shadow-sm)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--color-gray-50)'
                      e.currentTarget.style.borderColor = 'var(--color-gray-300)'
                      e.currentTarget.style.color = 'var(--color-gray-800)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-md)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'white'
                      e.currentTarget.style.borderColor = 'var(--color-gray-200)'
                      e.currentTarget.style.color = 'var(--color-gray-600)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                    }}>
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="group-hover/edit:rotate-12 transition-transform duration-300">
                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
                <path d="m15 5 4 4"/>
              </svg>
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent style={{ padding: 'var(--padding-card-lg)', gap: 'var(--space-8)' }} className="space-y-8">
        {/* 💎 Enhanced Main Amount Section - Clean Design */}
        <div className="relative rounded-2xl border shadow-inner overflow-hidden"
             style={{
               background: 'linear-gradient(135deg, var(--color-wants-50) 0%, var(--color-wants-100) 100%)',
               borderColor: 'rgba(220, 38, 38, 0.2)',
               padding: 'var(--space-8)'
             }}>
          {/* Top border accent */}
          <div className="absolute top-0 left-0 right-0 h-1 rounded-t-2xl"
               style={{ background: 'linear-gradient(90deg, var(--color-wants-500), var(--color-wants-600))' }}></div>

          <div className="relative space-y-6" style={{ gap: 'var(--space-6)' }}>
            {/* Header Section */}
            <div style={{ gap: 'var(--space-3)' }} className="space-y-3">
              {/* Main Amount */}
              <div className="text-display-lg font-bold tracking-tighter leading-[1.1]"
                   style={{ color: 'var(--color-error-800)' }}>
                {currency}{totalExpense.toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </div>

              {/* Over Budget Indicator */}
              <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
                <div className="flex items-center justify-center w-6 h-6 rounded-lg"
                     style={{
                       backgroundColor: 'rgba(220, 38, 38, 0.15)',
                       color: 'var(--color-error-600)'
                     }}>
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                    <path d="M17 17L7 7M7 7H17M7 7V17"/>
                  </svg>
                </div>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-error-600)' }}>
                  36.8%
                </span>
                <span className="text-body-md font-medium"
                      style={{ color: 'var(--color-gray-600)' }}>
                  Over Budget
                </span>
              </div>
            </div>

            {/* Overspent Section */}
            <div className="rounded-2xl border"
                 style={{
                   background: 'rgba(255, 255, 255, 0.6)',
                   borderColor: 'rgba(220, 38, 38, 0.2)',
                   padding: 'var(--space-5)'
                 }}>
              <div className="text-label-md font-medium"
                   style={{
                     color: 'var(--color-gray-500)',
                     marginBottom: 'var(--space-2)'
                   }}>
                Overspent This Month
              </div>
              <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-error-600)' }}>
                  +
                </span>
                <span className="text-heading-lg font-bold"
                      style={{ color: 'var(--color-error-800)' }}>
                  {currency}{overspentAmount.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 📊 Enhanced Top Expenses - Design System Compliant */}
        <div style={{ gap: 'var(--space-5)' }} className="space-y-5">
          <div className="flex items-center justify-between">
            <h4 className="text-heading-md font-semibold tracking-tight"
                style={{ color: 'var(--color-gray-900)' }}>
              Top 3 Expenses
            </h4>
            <div className="flex items-center" style={{ gap: 'var(--space-2)' }}>
              <span className="text-label-md font-medium"
                    style={{ color: 'var(--color-gray-500)' }}>
                3 categories
              </span>
              <div className="w-2 h-2 rounded-full animate-pulse"
                   style={{ backgroundColor: 'var(--color-wants-400)' }}></div>
            </div>
          </div>
          <div style={{ gap: 'var(--space-4)' }} className="space-y-4">
            {topExpenses.map((item, index) => (
              <div key={index} className="group relative bg-white rounded-2xl border transition-all duration-300 shadow-sm hover:shadow-md overflow-hidden"
                   style={{
                     borderColor: 'var(--color-gray-200)',
                   }}
                   onMouseEnter={(e) => {
                     e.currentTarget.style.borderColor = 'rgba(220, 38, 38, 0.2)'
                     e.currentTarget.style.boxShadow = 'var(--shadow-md)'
                   }}
                   onMouseLeave={(e) => {
                     e.currentTarget.style.borderColor = 'var(--color-gray-200)'
                     e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                   }}>

                {/* Top accent bar */}
                <div className="h-1"
                     style={{ background: 'linear-gradient(90deg, var(--color-wants-200), var(--color-wants-300))' }}></div>

                <div style={{ padding: 'var(--space-5)' }}>
                  {/* Header Row */}
                  <div className="flex items-center justify-between" style={{ marginBottom: 'var(--space-4)' }}>
                    <div className="flex items-center" style={{ gap: 'var(--space-3)' }}>
                      <ExpenseCategoryIcon
                        type={item.iconType || 'other'}
                        budgetType={item.budgetType}
                        className="group-hover:scale-105 transition-all duration-300 flex-shrink-0"
                      />
                      <div className="text-display-md font-semibold tracking-tight"
                           style={{ color: 'var(--color-gray-900)' }}>
                        {item.subcategory}
                      </div>
                    </div>

                    {/* Budget Type Badge */}
                    <div className="flex items-center rounded-full border"
                         style={{
                           backgroundColor: item.budgetType === 'NEED'
                             ? 'var(--color-need-50)'
                             : item.budgetType === 'WANT'
                             ? 'var(--color-wants-50)'
                             : 'var(--color-smile-50)',
                           borderColor: item.budgetType === 'NEED'
                             ? 'var(--color-need-200)'
                             : item.budgetType === 'WANT'
                             ? 'var(--color-wants-200)'
                             : 'var(--color-smile-200)',
                           padding: 'var(--space-1) var(--space-3)',
                           gap: 'var(--space-1)'
                         }}>
                      <div className="w-2 h-2 rounded-full"
                           style={{
                             backgroundColor: item.budgetType === 'NEED'
                               ? 'var(--color-need-500)'
                               : item.budgetType === 'WANT'
                               ? 'var(--color-wants-500)'
                               : 'var(--color-smile-500)'
                           }}></div>
                      <span className="text-label-md font-medium"
                            style={{
                              color: item.budgetType === 'NEED'
                                ? 'var(--color-need-700)'
                                : item.budgetType === 'WANT'
                                ? 'var(--color-wants-700)'
                                : 'var(--color-smile-700)'
                            }}>
                        {item.budgetType}
                      </span>
                    </div>
                  </div>

                  {/* Amount Row */}
                  <div className="flex items-end justify-between">
                    <div>
                      <div className="text-heading-md font-bold tracking-tight"
                           style={{ color: 'var(--color-gray-900)' }}>
                        {currency}{item.amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                      </div>
                      <div className="text-label-md font-medium"
                           style={{ color: 'var(--color-gray-500)' }}>
                        {Math.round((item.amount / totalExpense) * 100)}% of total
                      </div>
                    </div>

                    {/* Percentage Badge */}
                    <div className="flex items-center rounded-lg"
                         style={{
                           backgroundColor: 'var(--color-wants-50)',
                           padding: 'var(--space-2) var(--space-3)',
                           gap: 'var(--space-1)'
                         }}>
                      <span className="text-label-md font-bold"
                            style={{ color: 'var(--color-wants-600)' }}>
                        {item.percentage || Math.round((item.amount / totalExpense) * 100)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
