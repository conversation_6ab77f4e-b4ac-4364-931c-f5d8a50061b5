import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'

interface BudgetOverviewWidgetProps {
  totalBudget: number
  spent: number
  remaining: number
  currency?: string
}

export function BudgetOverviewWidget({ 
  totalBudget, 
  spent, 
  remaining, 
  currency = '$' 
}: BudgetOverviewWidgetProps) {
  const spentPercentage = (spent / totalBudget) * 100
  const remainingPercentage = 100 - spentPercentage

  return (
    <Card className="bg-gradient-to-br from-teal-50 to-teal-100 border-teal-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-teal-900">
          <span className="text-xl mr-2">📊</span>
          Budget Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Total Budget */}
          <div className="text-center">
            <div className="text-3xl font-bold text-teal-900">
              {currency}{totalBudget.toLocaleString()}
            </div>
            <div className="text-sm text-teal-700">Total Budget</div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-teal-700">Spent: {currency}{spent.toLocaleString()}</span>
              <span className="text-teal-700">{spentPercentage.toFixed(1)}%</span>
            </div>
            <Progress
              value={Math.min(spentPercentage, 100)}
              className="h-3"
            />
          </div>

          {/* Remaining Amount */}
          <div className="flex justify-between items-center pt-2">
            <div>
              <div className="text-lg font-semibold text-green-700">
                {currency}{remaining.toLocaleString()}
              </div>
              <div className="text-sm text-green-600">Remaining</div>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold text-teal-700">
                {remainingPercentage.toFixed(1)}%
              </div>
              <div className="text-sm text-teal-600">Available</div>
            </div>
          </div>

          {/* Status Indicator */}
          <div className="pt-2 border-t border-teal-200">
            {spentPercentage < 50 && (
              <Badge variant="secondary" className="bg-green-100 text-green-800 hover:bg-green-100">
                <span className="mr-1">✅</span>
                On Track
              </Badge>
            )}
            {spentPercentage >= 50 && spentPercentage < 80 && (
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
                <span className="mr-1">⚠️</span>
                Monitor Spending
              </Badge>
            )}
            {spentPercentage >= 80 && spentPercentage < 100 && (
              <Badge variant="secondary" className="bg-orange-100 text-orange-800 hover:bg-orange-100">
                <span className="mr-1">🚨</span>
                Approaching Limit
              </Badge>
            )}
            {spentPercentage >= 100 && (
              <Badge variant="destructive">
                <span className="mr-1">🔴</span>
                Budget Exceeded
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
