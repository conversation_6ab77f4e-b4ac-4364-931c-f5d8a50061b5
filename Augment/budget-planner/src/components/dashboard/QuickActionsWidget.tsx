import Link from 'next/link'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface QuickAction {
  name: string
  href: string
  icon: string
  description: string
  variant?: 'default' | 'outline'
}

const quickActions: QuickAction[] = [
  {
    name: 'Add Expense',
    href: '/expenses/add',
    icon: '➕',
    description: 'Record a new transaction',
    variant: 'default'
  },
  {
    name: 'View Budget',
    href: '/budget',
    icon: '📊',
    description: 'Check budget status',
    variant: 'outline'
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: '📈',
    description: 'View spending insights',
    variant: 'outline'
  },
  {
    name: 'Categories',
    href: '/categories',
    icon: '🏷️',
    description: 'Manage categories',
    variant: 'outline'
  }
]

export function QuickActionsWidget() {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center">
          <span className="text-xl mr-2">⚡</span>
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action) => (
            <Link key={action.name} href={action.href}>
              <Button
                variant={action.variant}
                className={cn(
                  "h-20 w-full flex flex-col items-center justify-center space-y-1 text-center",
                  action.variant === 'default'
                    ? "bg-teal-600 hover:bg-teal-700 text-white"
                    : "border-teal-200 text-teal-700 hover:bg-teal-50"
                )}
              >
                <span className="text-2xl">{action.icon}</span>
                <div>
                  <div className="font-medium text-sm">{action.name}</div>
                  <div className="text-xs opacity-75">{action.description}</div>
                </div>
              </Button>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
