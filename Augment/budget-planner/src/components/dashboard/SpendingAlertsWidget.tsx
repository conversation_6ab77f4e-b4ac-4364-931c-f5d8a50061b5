import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'

interface SpendingAlert {
  id: string
  category: string
  message: string
  severity: 'info' | 'warning' | 'danger'
  percentage: number
  amount: number
  limit: number
}

interface SpendingAlertsWidgetProps {
  alerts: SpendingAlert[]
  currency?: string
}

export function SpendingAlertsWidget({ 
  alerts, 
  currency = '$' 
}: SpendingAlertsWidgetProps) {
  const getSeverityStyles = (severity: string) => {
    switch (severity) {
      case 'danger':
        return 'bg-red-50 border-red-200 text-red-800'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800'
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'danger':
        return '🚨'
      case 'warning':
        return '⚠️'
      case 'info':
        return 'ℹ️'
      default:
        return '📢'
    }
  }

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      'Food & Dining': '🍔',
      'Transportation': '🚗',
      'Entertainment': '🎬',
      'Shopping': '🛍️',
      'Utilities': '🏠',
      'Healthcare': '🏥'
    }
    return icons[category] || '💳'
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center">
          <span className="text-xl mr-2">🚨</span>
          Spending Alerts
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {alerts.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              <div className="text-3xl mb-2">✅</div>
              <p className="text-sm font-medium">All Good!</p>
              <p className="text-xs text-gray-400">No spending alerts at the moment</p>
            </div>
          ) : (
            alerts.map((alert) => (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border ${getSeverityStyles(alert.severity)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-2">
                    <span className="text-lg">
                      {getSeverityIcon(alert.severity)}
                    </span>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm">
                          {getCategoryIcon(alert.category)}
                        </span>
                        <span className="font-medium text-sm">
                          {alert.category}
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {alert.percentage}%
                        </Badge>
                      </div>
                      <p className="text-sm">{alert.message}</p>
                      <div className="mt-2 text-xs">
                        <span className="font-medium">
                          {currency}{alert.amount.toLocaleString()}
                        </span>
                        <span className="text-opacity-75"> of </span>
                        <span className="font-medium">
                          {currency}{alert.limit.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-xs h-6 px-2 opacity-75 hover:opacity-100"
                  >
                    Dismiss
                  </Button>
                </div>
                
                {/* Progress bar */}
                <div className="mt-3">
                  <Progress
                    value={Math.min(alert.percentage, 100)}
                    className="h-2"
                  />
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
