// Budget API service layer
export interface BudgetData {
  id?: string
  name: string
  description?: string
  startDate: string
  endDate: string
  totalIncome: number
  strategy?: string
  selectedTemplate?: any
  incomeStreams?: any[]
  budgetCategories: BudgetCategoryData[]
}

export interface BudgetCategoryData {
  id?: string
  categoryId: string
  categoryName: string
  amount: number
  percentage: number
  spent?: number
  remaining?: number
  color?: string
  description?: string
}

export interface Budget {
  id: string
  name: string
  description?: string
  startDate: string
  endDate: string
  totalMonthlyIncome: number
  totalAllocated: number
  totalSpent: number
  remaining: number
  status: string
  createdAt: string
  categories: BudgetCategory[]
  isCustom?: boolean
  incomeStreams?: any[]
}

export interface BudgetCategory {
  id: string
  name: string
  description: string
  color: string
  percentage: number
  allocated: number
  spent: number
  remaining: number
  subcategories: any[]
}

export interface BudgetListItem {
  id: string
  name: string
  startDate: string
  endDate: string
  totalIncome: number
  totalAllocated: number
  totalSpent: number
  status: string
  createdAt: string
  categories: number
}

class BudgetApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message)
    this.name = 'BudgetApiError'
  }
}

// API service functions
export const budgetApi = {
  // Get all budgets for the current user
  async getBudgets(): Promise<BudgetListItem[]> {
    try {
      const response = await fetch('/api/budgets', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new BudgetApiError(error.error || 'Failed to fetch budgets', response.status)
      }

      const data = await response.json()
      return data.budgets
    } catch (error) {
      console.error('Error fetching budgets:', error)
      if (error instanceof BudgetApiError) {
        throw error
      }
      throw new BudgetApiError('Network error while fetching budgets')
    }
  },

  // Get a specific budget by ID
  async getBudget(id: string): Promise<Budget> {
    try {
      const response = await fetch(`/api/budgets/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new BudgetApiError(error.error || 'Failed to fetch budget', response.status)
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching budget:', error)
      if (error instanceof BudgetApiError) {
        throw error
      }
      throw new BudgetApiError('Network error while fetching budget')
    }
  },

  // Create a new budget
  async createBudget(budgetData: BudgetData): Promise<Budget> {
    try {
      console.log('🔄 Creating budget via API:', budgetData.name)
      
      const response = await fetch('/api/budgets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(budgetData),
      })

      if (!response.ok) {
        const error = await response.json()
        console.error('❌ Budget creation failed:', error)

        // Handle validation errors specifically
        if (error.details && Array.isArray(error.details)) {
          const validationMessages = error.details.map((detail: any) =>
            `${detail.path?.join('.')}: ${detail.message}`
          ).join(', ')
          throw new BudgetApiError(`Validation error: ${validationMessages}`, response.status)
        }

        throw new BudgetApiError(error.error || 'Failed to create budget', response.status)
      }

      const result = await response.json()
      console.log('✅ Budget created successfully via API:', result.budget.id)
      
      return result.budget
    } catch (error) {
      console.error('Error creating budget:', error)
      if (error instanceof BudgetApiError) {
        throw error
      }
      throw new BudgetApiError('Network error while creating budget')
    }
  },

  // Update an existing budget
  async updateBudget(id: string, budgetData: Partial<BudgetData>): Promise<Budget> {
    try {
      const response = await fetch(`/api/budgets/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(budgetData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new BudgetApiError(error.error || 'Failed to update budget', response.status)
      }

      const result = await response.json()
      return result.budget
    } catch (error) {
      console.error('Error updating budget:', error)
      if (error instanceof BudgetApiError) {
        throw error
      }
      throw new BudgetApiError('Network error while updating budget')
    }
  },

  // Delete a budget
  async deleteBudget(id: string): Promise<void> {
    try {
      const response = await fetch(`/api/budgets/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const error = await response.json()
        throw new BudgetApiError(error.error || 'Failed to delete budget', response.status)
      }

      console.log('✅ Budget deleted successfully:', id)
    } catch (error) {
      console.error('Error deleting budget:', error)
      if (error instanceof BudgetApiError) {
        throw error
      }
      throw new BudgetApiError('Network error while deleting budget')
    }
  }
}

// Helper function to transform localStorage budget data to API format
export function transformLocalStorageBudgetToApi(localBudget: any): BudgetData {
  return {
    name: localBudget.name,
    description: localBudget.description,
    startDate: localBudget.startDate,
    endDate: localBudget.endDate,
    totalIncome: localBudget.totalIncome,
    strategy: localBudget.strategy,
    selectedTemplate: localBudget.selectedTemplate,
    incomeStreams: localBudget.incomeStreams || [],
    budgetCategories: localBudget.budgetCategories?.map((cat: any) => ({
      categoryId: cat.categoryId || cat.id,
      categoryName: cat.categoryName,
      amount: cat.amount,
      percentage: cat.percentage,
      spent: cat.spent || 0,
      remaining: cat.remaining || cat.amount,
      color: cat.color,
      description: cat.description
    })) || []
  }
}

export { BudgetApiError }
