// 🏦 CSV Import & Transaction Processing Utilities

import { parse } from 'csv-parse/sync'
import { 
  RawTransaction, 
  ProcessedTransaction, 
  BankFormat, 
  SUPPORTED_BANK_FORMATS,
  DEFAULT_CATEGORIZATION_RULES,
  ImportStats,
  CategorizationRule
} from '@/types/transaction'

// 📁 CSV File Parser
export class CSVImporter {
  private bankFormat: BankFormat
  private categorizationRules: CategorizationRule[]

  constructor(bankFormatId: string = 'generic') {
    this.bankFormat = SUPPORTED_BANK_FORMATS.find(f => f.id === bankFormatId) || SUPPORTED_BANK_FORMATS[0]
    this.categorizationRules = DEFAULT_CATEGORIZATION_RULES
  }

  // 🔍 Auto-detect bank format from CSV headers
  static detectBankFormat(csvContent: string): BankFormat {
    const lines = csvContent.split('\n')
    const headers = lines[0].toLowerCase().split(',').map(h => h.trim().replace(/"/g, ''))
    
    for (const format of SUPPORTED_BANK_FORMATS) {
      const matchScore = this.calculateFormatMatchScore(headers, format)
      if (matchScore > 0.7) {
        return format
      }
    }
    
    return SUPPORTED_BANK_FORMATS.find(f => f.id === 'generic')!
  }

  private static calculateFormatMatchScore(headers: string[], format: BankFormat): number {
    let matches = 0
    let totalColumns = 0

    Object.values(format.columns).forEach(columnNames => {
      totalColumns++
      const hasMatch = columnNames.some(colName => 
        headers.some(header => header.includes(colName.toLowerCase()))
      )
      if (hasMatch) matches++
    })

    return matches / totalColumns
  }

  // 📊 Parse CSV file to raw transactions
  async parseCSV(file: File): Promise<RawTransaction[]> {
    const content = await file.text()
    
    try {
      const records = parse(content, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
        skip_records_with_empty_values: false
      })

      return records.map((record: any, index: number) => this.mapRecordToTransaction(record, index))
        .filter(transaction => transaction !== null) as RawTransaction[]
    } catch (error) {
      throw new Error(`CSV parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // 🗺️ Map CSV record to transaction
  private mapRecordToTransaction(record: any, index: number): RawTransaction | null {
    try {
      const date = this.extractDate(record)
      const description = this.extractDescription(record)
      const amount = this.extractAmount(record)

      if (!date || !description || amount === null) {
        console.warn(`Skipping invalid record at line ${index + 1}:`, record)
        return null
      }

      return {
        id: `import_${Date.now()}_${index}`,
        date,
        description,
        amount,
        type: amount < 0 ? 'expense' : 'income',
        balance: this.extractBalance(record),
        reference: this.extractReference(record)
      }
    } catch (error) {
      console.error(`Error processing record at line ${index + 1}:`, error)
      return null
    }
  }

  // 📅 Extract date from record
  private extractDate(record: any): string | null {
    for (const dateColumn of this.bankFormat.columns.date) {
      const value = record[dateColumn]
      if (value && this.isValidDate(value)) {
        return this.normalizeDate(value)
      }
    }
    return null
  }

  // 📝 Extract description from record
  private extractDescription(record: any): string | null {
    for (const descColumn of this.bankFormat.columns.description) {
      const value = record[descColumn]
      if (value && typeof value === 'string' && value.trim().length > 0) {
        return value.trim()
      }
    }
    return null
  }

  // 💰 Extract amount from record
  private extractAmount(record: any): number | null {
    for (const amountColumn of this.bankFormat.columns.amount) {
      const value = record[amountColumn]
      if (value !== undefined && value !== null) {
        const amount = this.parseAmount(value.toString())
        if (!isNaN(amount)) {
          return amount
        }
      }
    }
    return null
  }

  // 💳 Extract balance from record
  private extractBalance(record: any): number | null {
    if (!this.bankFormat.columns.balance) return null
    
    for (const balanceColumn of this.bankFormat.columns.balance) {
      const value = record[balanceColumn]
      if (value !== undefined && value !== null) {
        const balance = this.parseAmount(value.toString())
        if (!isNaN(balance)) {
          return balance
        }
      }
    }
    return null
  }

  // 🔗 Extract reference from record
  private extractReference(record: any): string | null {
    if (!this.bankFormat.columns.reference) return null
    
    for (const refColumn of this.bankFormat.columns.reference) {
      const value = record[refColumn]
      if (value && typeof value === 'string' && value.trim().length > 0) {
        return value.trim()
      }
    }
    return null
  }

  // 🔢 Parse amount string to number
  private parseAmount(amountStr: string): number {
    // Remove currency symbols, commas, and extra spaces
    const cleaned = amountStr.replace(/[$,\s]/g, '').trim()
    
    // Handle parentheses as negative (accounting format)
    if (cleaned.startsWith('(') && cleaned.endsWith(')')) {
      return -parseFloat(cleaned.slice(1, -1))
    }
    
    return parseFloat(cleaned)
  }

  // 📅 Validate and normalize date
  private isValidDate(dateStr: string): boolean {
    const date = new Date(dateStr)
    return !isNaN(date.getTime())
  }

  private normalizeDate(dateStr: string): string {
    const date = new Date(dateStr)
    return date.toISOString().split('T')[0] // YYYY-MM-DD format
  }

  // 🤖 Smart categorization
  categorizeTransaction(transaction: RawTransaction): { category: string; subcategory: string; confidence: number } {
    const description = transaction.description.toLowerCase()
    let bestMatch = {
      category: 'Other',
      subcategory: 'Uncategorized',
      confidence: 0
    }

    for (const rule of this.categorizationRules) {
      let confidence = 0

      // Check keywords
      const keywordMatches = rule.keywords.filter(keyword => 
        description.includes(keyword.toLowerCase())
      ).length
      confidence += (keywordMatches / rule.keywords.length) * 0.6

      // Check patterns
      const patternMatches = rule.patterns.filter(pattern => 
        pattern.test(description)
      ).length
      confidence += (patternMatches / rule.patterns.length) * 0.4

      // Apply rule confidence and priority
      confidence *= rule.confidence
      confidence *= (rule.priority / 10) // Priority boost

      if (confidence > bestMatch.confidence) {
        bestMatch = {
          category: rule.category,
          subcategory: rule.subcategory,
          confidence
        }
      }
    }

    return bestMatch
  }

  // 📊 Process raw transactions to final format
  processTransactions(rawTransactions: RawTransaction[], userId: string): ProcessedTransaction[] {
    return rawTransactions.map(raw => {
      const categorization = this.categorizeTransaction(raw)
      
      return {
        id: raw.id || `processed_${Date.now()}_${Math.random()}`,
        date: new Date(raw.date),
        description: this.cleanDescription(raw.description),
        amount: Math.abs(raw.amount), // Store as positive, type indicates direction
        type: raw.amount < 0 ? 'expense' : 'income',
        category: {
          id: categorization.category.toLowerCase().replace(/\s+/g, '_'),
          name: categorization.category,
          type: this.getCategoryType(categorization.category),
          icon: this.getCategoryIcon(categorization.category),
          color: this.getCategoryColor(categorization.category),
          keywords: [],
          subcategories: []
        },
        subcategory: categorization.subcategory,
        confidence: categorization.confidence,
        isRecurring: this.detectRecurring(raw.description),
        merchantName: this.extractMerchantName(raw.description),
        originalDescription: raw.description,
        userId,
        tags: [],
        isReviewed: categorization.confidence > 0.8,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })
  }

  // 🧹 Clean transaction description
  private cleanDescription(description: string): string {
    return description
      .replace(/\s+/g, ' ')
      .replace(/[*#]+/g, '')
      .trim()
  }

  // 🏪 Extract merchant name from description
  private extractMerchantName(description: string): string | undefined {
    // Common patterns for merchant names
    const patterns = [
      /^([A-Z\s&]+)\s+\d+/,  // "WALMART 1234"
      /^([A-Z\s&]+)\s*-/,    // "STARBUCKS -"
      /^([A-Z\s&]+)\s*\*/    // "TARGET *"
    ]

    for (const pattern of patterns) {
      const match = description.match(pattern)
      if (match) {
        return match[1].trim()
      }
    }

    return undefined
  }

  // 🔄 Detect recurring transactions
  private detectRecurring(description: string): boolean {
    const recurringKeywords = ['subscription', 'monthly', 'recurring', 'autopay', 'automatic']
    return recurringKeywords.some(keyword => 
      description.toLowerCase().includes(keyword)
    )
  }

  // 🎨 Get category type
  private getCategoryType(category: string): 'NEED' | 'WANT' | 'SMILE' {
    const needCategories = ['Housing', 'Utilities', 'Groceries', 'Transportation', 'Healthcare']
    const smileCategories = ['Savings', 'Investment', 'Emergency Fund']
    
    if (needCategories.includes(category)) return 'NEED'
    if (smileCategories.includes(category)) return 'SMILE'
    return 'WANT'
  }

  // 🎨 Get category icon
  private getCategoryIcon(category: string): string {
    const iconMap: { [key: string]: string } = {
      'Housing': 'home',
      'Utilities': 'zap',
      'Food & Dining': 'utensils',
      'Transportation': 'car',
      'Healthcare': 'heart',
      'Entertainment': 'film',
      'Shopping': 'shopping-bag',
      'Savings': 'piggy-bank'
    }
    return iconMap[category] || 'circle'
  }

  // 🎨 Get category color
  private getCategoryColor(category: string): string {
    const colorMap: { [key: string]: string } = {
      'Housing': '#3B82F6',
      'Utilities': '#10B981',
      'Food & Dining': '#F59E0B',
      'Transportation': '#EF4444',
      'Healthcare': '#8B5CF6',
      'Entertainment': '#EC4899',
      'Shopping': '#F97316',
      'Savings': '#059669'
    }
    return colorMap[category] || '#6B7280'
  }
}
