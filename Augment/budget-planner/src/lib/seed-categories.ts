import { prisma } from './prisma'

// Default categories for budget system
const defaultCategories = [
  {
    name: 'Needs',
    description: 'Essential expenses you cannot avoid',
    subcategories: [
      { name: 'Housing', description: 'Rent, mortgage, property taxes' },
      { name: 'Utilities', description: 'Electricity, water, gas, internet' },
      { name: 'Groceries', description: 'Food and household essentials' },
      { name: 'Transportation', description: 'Car payments, gas, public transit' },
      { name: 'Healthcare', description: 'Insurance, medical expenses' },
      { name: 'Insurance', description: 'Life, auto, home insurance' },
      { name: 'Minimum Debt Payments', description: 'Required loan and credit card payments' }
    ]
  },
  {
    name: 'Wants',
    description: 'Things you enjoy but could live without',
    subcategories: [
      { name: 'Dining Out', description: 'Restaurants, takeout, coffee shops' },
      { name: 'Entertainment', description: 'Movies, concerts, streaming services' },
      { name: 'Shopping', description: 'Clothing, electronics, non-essentials' },
      { name: 'Hobbies', description: 'Sports, crafts, recreational activities' },
      { name: 'Travel', description: 'Vacations, weekend trips' },
      { name: 'Personal Care', description: 'Salon, spa, cosmetics' },
      { name: 'Subscriptions', description: 'Non-essential monthly services' }
    ]
  },
  {
    name: 'Smile',
    description: 'Building your financial future and security',
    subcategories: [
      { name: 'Emergency Fund', description: '3-6 months of expenses' },
      { name: 'Retirement Savings', description: '401k, IRA contributions' },
      { name: 'Investments', description: 'Stocks, bonds, mutual funds' },
      { name: 'Debt Payoff', description: 'Extra payments on loans and credit cards' },
      { name: 'Savings Goals', description: 'House down payment, car fund' },
      { name: 'Education', description: 'Courses, certifications, books' }
    ]
  }
]

export async function seedCategories() {
  console.log('🌱 Seeding default categories...')
  
  try {
    for (const categoryData of defaultCategories) {
      // Check if category already exists
      const existingCategory = await prisma.category.findFirst({
        where: { name: categoryData.name }
      })

      if (existingCategory) {
        console.log(`✅ Category "${categoryData.name}" already exists`)
        continue
      }

      // Create main category
      const category = await prisma.category.create({
        data: {
          name: categoryData.name,
          description: categoryData.description
        }
      })

      console.log(`✅ Created category: ${category.name}`)

      // Create subcategories
      for (const subData of categoryData.subcategories) {
        const subcategory = await prisma.category.create({
          data: {
            name: subData.name,
            description: subData.description,
            parentId: category.id
          }
        })
        console.log(`  ✅ Created subcategory: ${subcategory.name}`)
      }
    }

    console.log('🎉 Category seeding completed!')
    
  } catch (error) {
    console.error('❌ Error seeding categories:', error)
    throw error
  }
}

// Function to get or create a category by name
export async function getOrCreateCategory(name: string, description?: string) {
  let category = await prisma.category.findFirst({
    where: { name }
  })

  if (!category) {
    category = await prisma.category.create({
      data: {
        name,
        description: description || `Budget category: ${name}`
      }
    })
    console.log(`✅ Created new category: ${name}`)
  }

  return category
}
