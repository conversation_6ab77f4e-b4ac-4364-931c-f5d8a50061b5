{"name": "budget-planner", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset --force", "db:studio": "prisma studio"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.9.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.513.0", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1", "zod": "^3.25.64"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.9.0", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}