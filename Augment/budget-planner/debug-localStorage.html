<!DOCTYPE html>
<html>
<head>
    <title>Debug localStorage</title>
</head>
<body>
    <h1>Budget Data Debug</h1>
    <div id="output"></div>
    
    <script>
        const budgets = localStorage.getItem('budgets');
        const output = document.getElementById('output');
        
        if (budgets) {
            const budgetList = JSON.parse(budgets);
            output.innerHTML = '<h2>Found ' + budgetList.length + ' budgets:</h2>';
            
            budgetList.forEach((budget, index) => {
                output.innerHTML += '<h3>Budget ' + (index + 1) + ': ' + budget.name + '</h3>';
                output.innerHTML += '<p><strong>ID:</strong> ' + budget.id + '</p>';
                output.innerHTML += '<p><strong>Strategy:</strong> ' + budget.strategy + '</p>';
                output.innerHTML += '<p><strong>Selected Template:</strong> ' + JSON.stringify(budget.selectedTemplate, null, 2) + '</p>';
                output.innerHTML += '<p><strong>Categories:</strong></p>';
                output.innerHTML += '<pre>' + JSON.stringify(budget.budgetCategories, null, 2) + '</pre>';
                output.innerHTML += '<hr>';
            });
        } else {
            output.innerHTML = '<p>No budgets found in localStorage</p>';
        }
    </script>
</body>
</html>
